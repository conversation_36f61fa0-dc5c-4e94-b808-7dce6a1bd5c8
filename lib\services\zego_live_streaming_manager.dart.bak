import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import '../internal/business/business_define.dart';
import '../internal/sdk/utils/flutter_extension.dart';
import '../utils/zegocloud_token.dart';
import '../zego_sdk_manager.dart';
import 'package:zego_express_engine/zego_express_engine.dart';

// Simplified Zego Live Streaming Manager
class ZegoLiveStreamingManager {
  ZegoLiveStreamingManager._internal();
  factory ZegoLiveStreamingManager() => instance;
  static final ZegoLiveStreamingManager instance = ZegoLiveStreamingManager._internal();

  ValueNotifier<ZegoLiveStreamingRole> currentUserRoleNoti = ValueNotifier(ZegoLiveStreamingRole.audience);
  ValueNotifier<ZegoSDKUser?> hostNoti = ValueNotifier(null);
  ValueNotifier<bool> isLivingNotifier = ValueNotifier(false);
  List<StreamSubscription> subscriptions = [];

  String _currentRoomID = '';
  String _currentStreamID = '';

  final StreamController<ZegoRoomStateEvent> _roomStateUpdateStreamCtrl = StreamController<ZegoRoomStateEvent>.broadcast();
  final StreamController<ZegoRoomUserListUpdateEvent> _roomUserListUpdateStreamCtrl =
      StreamController<ZegoRoomUserListUpdateEvent>.broadcast();
  final StreamController<ZegoIMRecvCustomCommandEvent> _roomCustomCommandStreamCtrl =
      StreamController<ZegoIMRecvCustomCommandEvent>.broadcast();

  Stream<ZegoRoomStateEvent> get onRoomStateUpdateEvent => _roomStateUpdateStreamCtrl.stream;
  Stream<ZegoRoomUserListUpdateEvent> get onRoomUserUpdateEvent => _roomUserListUpdateStreamCtrl.stream;
  Stream<ZegoIMRecvCustomCommandEvent> get onReceiveRoomCustomCommandEvent => _roomCustomCommandStreamCtrl.stream;

  void _listenEvents() {
    subscriptions.add(ZegoExpressEngine.onEvent.listen(_handleSDKEvent));

    subscriptions
      ..add(onRoomStateUpdateEvent.listen(onRoomStateUpdate))
      ..add(onRoomUserUpdateEvent.listen(onRoomUserUpdate))
      ..add(onReceiveRoomCustomCommandEvent.listen(onRoomCustomCommandMessageHandler));
  }
  
  void _handleSDKEvent(ZegoEvent event) {
    final data = event.data; // Data is dynamic, structure depends on event.method
    switch (event.method) {
      case 'onRoomStateUpdate':
        if (data is List && data.length == 4) {
          String roomID = data[0] as String;
          ZegoRoomState state = data[1] as ZegoRoomState;
          int errorCode = data[2] as int;
          Map<String, dynamic> extendedData = Map<String, dynamic>.from(data[3] as Map);

          // Map ZegoRoomState to ZegoRoomStateChangedReason for our internal event
          ZegoRoomStateChangedReason reason = ZegoRoomStateChangedReason.noRetry; // Default if unknown
          if (state == ZegoRoomState.disconnected) reason = ZegoRoomStateChangedReason.logout;
          if (state == ZegoRoomState.connected) reason = ZegoRoomStateChangedReason.success;
          if (state == ZegoRoomState.connecting) reason = ZegoRoomStateChangedReason.connecting;
          
          if (errorCode != 0 && reason == ZegoRoomStateChangedReason.success) {
             // If login succeeded but there's an error code, it might be a specific login failure reason
             reason = ZegoRoomStateChangedReason.failed; // Or map specific error codes
          }

          _roomStateUpdateStreamCtrl.add(ZegoRoomStateEvent(roomID, reason, errorCode, extendedData));
        }
        break;
      case 'onRoomUserUpdate':
        if (data is List && data.length == 3) {
          String roomID = data[0] as String;
          ZegoUpdateType updateType = data[1] as ZegoUpdateType;
          List<ZegoUser> userList = (data[2] as List).map((u) {
            final map = Map<String, dynamic>.from(u as Map);
            return ZegoUser(map['userID'] as String, map['userName'] as String);
          }).toList();
          _roomUserListUpdateStreamCtrl.add(ZegoRoomUserListUpdateEvent(roomID, updateType, userList));
        }
        break;
      case 'onIMRecvCustomCommand':
        if (data is List && data.length == 3) {
          String roomID = data[0] as String;
          final fromUserMap = Map<String, dynamic>.from(data[1] as Map);
          ZegoUser fromUser = ZegoUser(fromUserMap['userID'] as String, fromUserMap['userName'] as String);
          String command = data[2] as String;
          _roomCustomCommandStreamCtrl.add(ZegoIMRecvCustomCommandEvent(roomID, fromUser, command));
        }
        break;
      default:
        break;
    }
  }

  void _unlistenEvents() {
    for (final subscription in subscriptions) {
      subscription.cancel();
    }
  }

  String generateStreamID(String userID, String roomID) {
    return '$roomID-$userID-${DateTime.now().millisecondsSinceEpoch}';
  }

  void init() {
    _listenEvents();
  }

  void startPreview() {
    ZegoExpressEngine.instance.startPreview();
  }

  void stopPreview() {
    ZegoExpressEngine.instance.stopPreview();
  }

  void startPublishingStream(String streamID) {
    _currentStreamID = streamID;
    ZegoExpressEngine.instance.startPublishingStream(streamID);
  }

  void stopPublishingStream() {
    if (_currentStreamID.isNotEmpty) {
      ZegoExpressEngine.instance.stopPublishingStream();
      _currentStreamID = '';
    }
  }

  void uninit() {
    _unlistenEvents();
  }

  void onRoomStateUpdate(ZegoRoomStateEvent event) {
    if (event.errorCode == 0 && event.reason == ZegoRoomStateChangedReason.success) {
      if (currentUserRoleNoti.value == ZegoLiveStreamingRole.host) {
        ZegoExpressEngine.instance.startPublishingStream(_currentStreamID);
      }
    }
    if (event.reason == ZegoRoomStateChangedReason.Logout) {
      isLivingNotifier.value = false;
    }
  }

  void onRoomUserUpdate(ZegoRoomUserListUpdateEvent event) {
    // Handle user updates if needed
  }

  void onRoomCustomCommandMessageHandler(ZegoIMRecvCustomCommandEvent event) {
    // Handle custom commands if needed
  }

  Future<ZegoRoomLoginResult> joinRoom(String roomID, {String? token}) async {
    _currentRoomID = roomID;
    String? streamID;
    streamID = generateStreamID(ZEGOSDKManager().currentUser!.userID, roomID);
    _currentStreamID = streamID!;
    final user = ZegoUser(ZEGOSDKManager().currentUser!.userID, ZEGOSDKManager().currentUser!.userName);
    final roomConfig = ZegoRoomConfig(0, false, token ?? ''); // isUserStatusNotify: false for ZegoScenario.LiveStreaming
    final result = await ZegoExpressEngine.instance.loginRoom(roomID, user, config: roomConfig);
    if (result.errorCode == 0) {
      _currentRoomID = roomID;
    }
    if (result.errorCode == 0) {
      isLivingNotifier.value = true;
      ZegoExpressEngine.instance.startPublishingStream(streamID);
    }
    return result;
  }

  Future<void> leaveRoom() async {
    stopPreview();
    stopPublishingStream();
    hostNoti.value = null;
    isLivingNotifier.value = false;
    currentUserRoleNoti.value = ZegoLiveStreamingRole.audience;
    _currentRoomID = '';
    _currentStreamID = '';
    await ZegoExpressEngine.instance.logoutRoom();
  }
  
  Future<ZegoRoomLogoutResult> stopLive() async {
    return await leaveRoom().then((_) => ZegoRoomLogoutResult(0));
  }

  Future<void> sendSEI(Map<String, dynamic> seiData) async {
    if (_currentRoomID.isEmpty) {
      debugPrint('Error: Not in a room, cannot send SEI.');
      return; 
    }
    final seiJsonString = jsonEncode(seiData);
    final seiBytes = Uint8List.fromList(utf8.encode(seiJsonString));
    await ZegoExpressEngine.instance.sendSEI(seiBytes, seiBytes.length, channel: ZegoPublishChannel.Main);
  }
}
