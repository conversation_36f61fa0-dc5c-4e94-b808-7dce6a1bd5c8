import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shortie/ui/loading_ui.dart';
import 'package:shortie/utils/utils.dart';

class CustomVideoPicker {
  static Future<String?> pickVideo() async {
    try {
      showDialog(
        context: Get.context!,
        useRootNavigator: true,
        barrierDismissible: false,
        builder: (context) => const LoadingUi(),
      );
      final videoPath = await ImagePicker().pickVideo(source: ImageSource.gallery);
      Get.back();

      if (videoPath != null) {
        return videoPath.path;
      } else {
        Utils.showLog("Video Not Selected !!");
        return null;
      }
    } catch (e) {
      Utils.showLog("Video Picker Error => $e");
      return null;
    }
  }
}
