import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:zego_express_engine/zego_express_engine.dart';

import '../../zego_sdk_manager.dart';

/// switch cameras
class ZegoSwitchCameraButton extends StatelessWidget {
  const ZegoSwitchCameraButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final currentUser = ZEGOSDKManager().currentUser;
    if (currentUser == null) return const SizedBox.shrink();
    
    return ValueListenableBuilder(
      valueListenable: currentUser.isUsingFrontCameraNotifier,
      builder: (BuildContext context, bool isUsingFrontCamera, Widget? child) {
        return GestureDetector(
          onTap: () {
            final newValue = !isUsingFrontCamera;
            currentUser.isUsingFrontCameraNotifier.value = newValue;
            ZegoExpressEngine.instance.useFrontCamera(newValue);
          },
          child: Container(
            width: 96,
            height: 96,
            decoration: BoxDecoration(
              color: const Color(0xff2C2F3E).withOpacity(0.6),
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white.withOpacity(0.3), width: 1),
            ),
            child: const Center(
              child: Icon(
                CupertinoIcons.switch_camera,
                color: Colors.white,
                size: 32,
              ),
            ),
          ),
        );
      },
    );
  }
}
