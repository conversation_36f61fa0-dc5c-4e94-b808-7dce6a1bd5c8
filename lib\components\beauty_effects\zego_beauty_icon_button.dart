import 'package:flutter/material.dart';
import '../../internal/internal_defines.dart';

/// Text button with optional icon and text
class ZegoTextIconButton extends StatelessWidget {
  final String? text;
  final TextStyle? textStyle;
  final ButtonIcon? icon;
  final Size? iconSize;
  final double? iconTextSpacing;
  final Size? buttonSize;
  final VoidCallback? onPressed;
  final bool isSelected;

  const ZegoTextIconButton({
    Key? key,
    this.text,
    this.textStyle,
    this.icon,
    this.iconTextSpacing,
    this.iconSize,
    this.buttonSize,
    this.onPressed,
    required this.isSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: buttonSize?.width ?? 120,
        height: buttonSize?.height ?? 120,
        padding: const EdgeInsets.all(8),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (icon != null)
              Builder(builder: (context) {
                // First check if we have a direct widget icon
                if (icon?.icon != null) {
                  return SizedBox(
                    width: iconSize?.width ?? 56,
                    height: iconSize?.height ?? (iconSize?.width ?? 56),
                    child: icon!.icon,
                  );
                }
                
                // Otherwise try to load from asset URL
                String? path;
                if (isSelected) {
                  path = icon?.selectedIconUrl;
                }
                if (path == null || path.isEmpty) {
                  path = icon?.normalIconUrl;
                }

                if (path != null && path.isNotEmpty) {
                  return Image.asset(
                    path,
                    width: iconSize?.width ?? 56,
                    height: iconSize?.height ?? (iconSize?.width ?? 56),
                    errorBuilder: (context, error, stackTrace) {
                      // Fallback to a generic icon if asset loading fails
                      return Icon(
                        Icons.face_retouching_natural,
                        color: Colors.white,
                        size: 32,
                      );
                    },
                  );
                }
                
                // Fallback to a visible placeholder
                return Icon(
                  Icons.question_mark,
                  color: Colors.white,
                  size: 32,
                );
              }),
            if (text?.isNotEmpty ?? false) ...[
              SizedBox(height: iconTextSpacing ?? 8),
              Text(
                text!,
                style: textStyle ??
                    const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      decoration: TextDecoration.none,
                    ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
