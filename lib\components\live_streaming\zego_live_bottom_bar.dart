import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:zego_express_engine/zego_express_engine.dart';
import 'package:zego_effects_plugin/zego_effects_plugin.dart';

// Local imports
import '../../internal/sdk/effect/zego_effects_service.dart';
import '../beauty_effects/zego_beauty_effect_button.dart';
import '../beauty_effects/zego_beauty_effect_sheet.dart';
import '../common/zego_switch_camera_button.dart';
import '../common/zego_toggle_camera_button.dart';
import '../common/zego_toggle_microphone_button.dart';
// Gift feature removed

// Local models and managers
import '../../services/zego_role_manager.dart';
import '../../zego_sdk_manager.dart';

class ZegoLiveBottomBar extends StatefulWidget {
  const ZegoLiveBottomBar({
    this.applying,
    super.key,
  });

  final ValueNotifier<bool>? applying;

  @override
  State<ZegoLiveBottomBar> createState() => _ZegoLiveBottomBarState();
}


class _ZegoLiveBottomBarState extends State<ZegoLiveBottomBar> {
  // RoomRequest? myRoomRequest; // ZIM related

  @override
  Widget build(BuildContext context) {
    print('ZegoLiveBottomBar building... Current user: ${ZEGOSDKManager().currentUser}');
    
    // Always show the bottom bar for debugging purposes
    return ValueListenableBuilder<ZegoLiveStreamingRole>(
      valueListenable: ZegoRoleManager().currentUserRoleNoti,
      builder: (context, role, _) {
        print('ZegoRoleManager role: $role');
        return getBottomBar(role);
      },
    );
    
    /* Original code with condition - uncomment later
    if (ZEGOSDKManager().currentUser == null) {
      print('ZegoLiveBottomBar not showing: currentUser is null');
      return const SizedBox.shrink();
    } else {
      return ValueListenableBuilder<ZegoLiveStreamingRole>(
        valueListenable: ZegoRoleManager().currentUserRoleNoti,
        builder: (context, role, _) {
          return getBottomBar(role);
        },
      );
    }
    */
  }

  Widget getBottomBar(ZegoLiveStreamingRole role) {
    return buttonView(role);
  }

  Widget buttonView(ZegoLiveStreamingRole role) {
    return Container(
      height: 60,
      // Completely transparent - no background decoration
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          beautyEffectButton(),
          // Add right padding
          const SizedBox(width: 20),
        ],
      ),
    );
    
    /* Original conditional code - uncomment later
    if (role == ZegoLiveStreamingRole.host) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          beautyEffectButton(),
          toggleMicButton(),
          toggleCameraButton(),
          if (!kIsWeb) switchCameraButton(),
        ],
      );
    } else if (role == ZegoLiveStreamingRole.audience) {
      return ValueListenableBuilder<bool>(
        valueListenable: widget.applying ?? ValueNotifier<bool>(false),
        builder: (context, state, _) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              const SizedBox(width: 50, height: 50),
              const SizedBox(width: 50, height: 50),
              const SizedBox(width: 50, height: 50), // Gift button removed
              // if (state) cancelApplyCohostButton() else applyCoHostButton(), // ZIM related
              const SizedBox(width: 50, height: 50), // Placeholder for cohost buttons
            ],
          );
        },
      );
    } else {*/
    /*
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          beautyEffectButton(),
          toggleMicButton(),
          toggleCameraButton(),
          switchCameraButton(),
          const SizedBox(width: 50, height: 50), // Gift button removed
          endCohostButton(),
        ],
      );
    }*/
  }

  // Gift button removed
  // Widget giftButton() {
  //   return SizedBox();
  // }



  Widget beautyEffectButton() {
    return LayoutBuilder(builder: (context, constrains) {
      return SizedBox(
        width: 50,
        height: 50,
        child: ZegoBeautyEffectButton(
          onPressed: () {
            showBeautyEffectSheet(context);
          },
        ),
      );
    });
  }

    // Widget applyCoHostButton() { // ZIM related
  //   return OutlinedButton(
  //       style: OutlinedButton.styleFrom(side: const BorderSide(width: 1, color: Colors.white)),
  //       onPressed: () {
  //         // final signaling = jsonEncode({
  //         //   'room_request_type': RoomRequestType.audienceApplyToBecomeCoHost,
  //         // });
  //         // ZEGOSDKManager()
  //         //     .zimService
  //         //     .sendRoomRequest(ZegoLiveStreamingManager().hostNoti.value?.userID ?? '', signaling)
  //         //     .then((value) {
  //         //   widget.applying?.value = true;
  //         //   myRoomRequest = ZEGOSDKManager().zimService.roomRequestMapNoti.value[value.requestID];
  //         // }).catchError((error) {
  //         //   ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('apply to co-host failed: $error')));
  //         // });
  //       },
  //       child: const Text(
  //         'Apply to co-host',
  //         style: TextStyle(
  //           color: Colors.white,
  //         ),
  //       ));
  // }

  // Widget cancelApplyCohostButton() { // ZIM related
  //   return OutlinedButton(
  //       style: OutlinedButton.styleFrom(side: const BorderSide(width: 1, color: Colors.white)),
  //       onPressed: () {
  //         // ZEGOSDKManager().zimService.cancelRoomRequest(myRoomRequest?.requestID ?? '').then((value) {
  //         //   widget.applying?.value = false;
  //         // }).catchError((error) {
  //         //   ScaffoldMessenger.of(context)
  //         //       .showSnackBar(SnackBar(content: Text('Cancel the application failed: $error')));
  //         // });
  //       },
  //       child: const Text('Cancel the application', style: TextStyle(color: Colors.white)));
  // }

  Widget endCohostButton() {
    return OutlinedButton(
      style: OutlinedButton.styleFrom(side: const BorderSide(width: 1, color: Colors.white)),
      onPressed: () {
        // Set role back to audience
        ZegoRoleManager().setRole(ZegoLiveStreamingRole.audience);
        // Stop preview and publishing
        ZegoExpressEngine.instance.stopPreview();
        ZegoExpressEngine.instance.stopPublishingStream();
      },
      child: const Text('End co-host', style: TextStyle(color: Colors.white)),
    );
  }
}
