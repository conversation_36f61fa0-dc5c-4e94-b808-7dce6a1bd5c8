import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:shortie/utils/utils.dart';
import 'package:zego_express_engine/zego_express_engine.dart';

import 'internal/sdk/effect/zego_effects_service.dart';

/// A simple user model for Zego SDK
class ZegoSDKUser {
  final String userID;
  final String userName;
  final ValueNotifier<bool> isCamerOnNotifier = ValueNotifier<bool>(false);
  final ValueNotifier<bool> isMicOnNotifier = ValueNotifier<bool>(true);
  final ValueNotifier<bool> isUsingFrontCameraNotifier = ValueNotifier<bool>(true); // Added missing notifier
  String? streamID;

  ZegoSDKUser({
    required this.userID,
    required this.userName,
    this.streamID,
  });
}

class ZEGOSDKManager {
  ZEGOSDKManager._internal();
  factory ZEGOSDKManager() => instance;
  static final ZEGOSDKManager instance = ZEGOSDKManager._internal();

  final EffectsService _effectsService = EffectsService();
  final List<ZegoSDKUser> _userInfoList = [];
  ZegoSDKUser? _currentUser;

  /// Get the current user
  ZegoSDKUser? get currentUser => _currentUser;
  
  /// Get list of users in the room
  List<ZegoSDKUser> get userInfoList => List.unmodifiable(_userInfoList);

  /// Initialize the SDK manager
  Future<void> init({bool initializeEffects = true}) async {
    // Engine is already initialized in Utils.onInitCreateEngine()
    if (initializeEffects && Utils.isShowReelsEffect) {
      await initEffects();
    }
  }

  /// Initialize effects
  Future<void> initEffects() async {
    if (!kIsWeb && Utils.isShowReelsEffect) {
      await _effectsService.init(Utils.liveAppId, Utils.liveAppSign);
            // Ensure ZegoVideoBufferType.zegoVideoBufferTypeGLTexture2D is correct for your SDK version
      final config = ZegoCustomVideoProcessConfig(ZegoVideoBufferType.GLTexture2D);
      await ZegoExpressEngine.instance.enableCustomVideoProcessing(true, config, channel: ZegoPublishChannel.Main);
    }
  }

  /// Uninitialize effects
  Future<void> unInitEffects() async {
    if (!kIsWeb) {
      await _effectsService.unInit();
            // Ensure ZegoVideoBufferType.zegoVideoBufferTypeGLTexture2D is correct for your SDK version
      final config = ZegoCustomVideoProcessConfig(ZegoVideoBufferType.GLTexture2D);
      await ZegoExpressEngine.instance.enableCustomVideoProcessing(false, config, channel: ZegoPublishChannel.Main);
    }
  }

  /// Connect a user
  Future<void> connectUser(String userID, String userName, {String? token}) async {
    _currentUser = ZegoSDKUser(userID: userID, userName: userName);
    _userInfoList.add(_currentUser!);
  }

  /// Disconnect the current user
  Future<void> disconnectUser() async {
    // Make sure to call logoutRoom() from LiveView before calling this
    _userInfoList.remove(_currentUser);
    _currentUser = null;
  }

  /// Upload logs
  Future<void> uploadLog() async {
    await ZegoExpressEngine.instance.uploadLog();
  }


  /// Get user by ID
  ZegoSDKUser? getUser(String userID) {
        try {
      return _userInfoList.firstWhere((user) => user.userID == userID);
    } catch (e) {
      return null;
    }
  }

  /// Get the effects service instance
  EffectsService get effectsService => _effectsService;
}
