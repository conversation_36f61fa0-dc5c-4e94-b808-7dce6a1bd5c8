import 'dart:typed_data';

import 'package:flutter/cupertino.dart';
import 'package:zego_express_engine/zego_express_engine.dart';

export 'package:zego_express_engine/zego_express_engine.dart';

export 'sdk/basic/zego_sdk_user.dart';
// Express service removed - export 'sdk/express/express_service.dart';

class ZegoRoomUserListUpdateEvent {
  final String roomID;
  final ZegoUpdateType updateType;
  final List<ZegoUser> userList;

  ZegoRoomUserListUpdateEvent(
    this.roomID,
    this.updateType,
    this.userList,
  );

  @override
  String toString() {
    return 'ZegoRoomUserListUpdateEvent{roomID: $roomID, updateType: ${updateType.name}, userList: ${userList.map((e) => '${e.userID}(${e.userName}),')}}';
  }
}

class ZegoRoomStreamListUpdateEvent {
  final String roomID;
  final ZegoUpdateType updateType;
  final List<ZegoStream> streamList;
  final Map<String, dynamic> extendedData;

  ZegoRoomStreamListUpdateEvent(this.roomID, this.updateType, this.streamList, this.extendedData);

  @override
  String toString() {
    return 'ZegoRoomStreamListUpdateEvent{roomID: $roomID, updateType: ${updateType.name}, streamList: ${streamList.map((e) => '${e.streamID}(${e.extraInfo}),')}';
  }
}

class ZegoRoomStreamExtraInfoEvent {
  final String roomID;
  final List<ZegoStream> streamList;

  ZegoRoomStreamExtraInfoEvent(this.roomID, this.streamList);

  @override
  String toString() {
    return 'ZegoRoomStreamExtraInfoEvent{roomID: $roomID, streamList: ${streamList.map((e) => '${e.streamID}(${e.extraInfo}),')}}';
  }
}

class ZegoRoomStateEvent {
  final String roomID;
  final ZegoRoomStateChangedReason reason;
  final int errorCode;
  final Map<String, dynamic> extendedData;

  ZegoRoomStateEvent(this.roomID, this.reason, this.errorCode, this.extendedData);

  @override
  String toString() {
    return 'ZegoRoomStateEvent{roomID: $roomID, reason: ${reason.name}, errorCode: $errorCode, extendedData: $extendedData}';
  }
}

class ZegoRoomExtraInfoEvent {
  final List<ZegoRoomExtraInfo> roomExtraInfoList;
  final String roomID;

  ZegoRoomExtraInfoEvent(this.roomID, this.roomExtraInfoList);

  @override
  String toString() {
    return 'ZegoRoomExtraInfoEvent{roomID: $roomID, roomExtraInfoList: ${roomExtraInfoList.map((e) => '${e.key}(${e.value}),')}}';
  }
}

class ZegoPublisherStateUpdateEvent {
  final String streamID;
  final ZegoPublisherState state;
  final int errorCode;
  final Map<String, dynamic> extendedData;

  ZegoPublisherStateUpdateEvent(this.streamID, this.state, this.errorCode, this.extendedData);

  @override
  String toString() {
    return 'ZegoPublisherStateUpdateEvent{streamID: $streamID, state: ${state.name}, errorCode: $errorCode, extendedData: $extendedData}';
  }
}

class ZegoPublisherQualityUpdateEvent {
  final String streamID;
  final ZegoPublishStreamQuality quality;

  ZegoPublisherQualityUpdateEvent(this.streamID, this.quality);

  @override
  String toString() {
    return 'ZegoPublisherQualityUpdateEvent{streamID: $streamID, quality: ${quality.toString()}}';
  }
}

class ZegoPlayerStateUpdateEvent {
  final String streamID;
  final ZegoPlayerState state;
  final int errorCode;
  final Map<String, dynamic> extendedData;

  ZegoPlayerStateUpdateEvent(this.streamID, this.state, this.errorCode, this.extendedData);

  @override
  String toString() {
    return 'ZegoPlayerStateUpdateEvent{streamID: $streamID, state: ${state.name}, errorCode: $errorCode, extendedData: $extendedData}';
  }
}

class ZegoPlayerQualityUpdateEvent {
  final String streamID;
  final ZegoPlayStreamQuality quality;

  ZegoPlayerQualityUpdateEvent(this.streamID, this.quality);

  @override
  String toString() {
    return 'ZegoPlayerQualityUpdateEvent{streamID: $streamID, quality: ${quality.toString()}}';
  }
}

class ZegoMixerSoundLevelUpdateEvent {
  final Map<int, double> soundLevels;

  ZegoMixerSoundLevelUpdateEvent(this.soundLevels);

  @override
  String toString() {
    return 'ZegoMixerSoundLevelUpdateEvent{soundLevels: $soundLevels}';
  }
}

class ZegoMediaPlayerStateUpdateEvent {
  final ZegoMediaPlayer mediaPlayer;
  final ZegoMediaPlayerState state;
  final int errorCode;

  ZegoMediaPlayerStateUpdateEvent(this.mediaPlayer, this.state, this.errorCode);

  @override
  String toString() {
    return 'ZegoMediaPlayerStateUpdateEvent{mediaPlayer: ${mediaPlayer.getIndex()}, state: ${state.name}, errorCode: $errorCode}';
  }
}

class ZegoMediaPlayerNetworkEventEvent {
  final ZegoMediaPlayer mediaPlayer;
  final ZegoMediaPlayerNetworkEvent networkEvent;

  ZegoMediaPlayerNetworkEventEvent(this.mediaPlayer, this.networkEvent);

  @override
  String toString() {
    return 'ZegoMediaPlayerNetworkEventEvent{mediaPlayer: ${mediaPlayer.getIndex()}, networkEvent: ${networkEvent.name}}';
  }
}

class ZegoMediaPlayerPlayingProgressEvent {
  final ZegoMediaPlayer mediaPlayer;
  final int millisecond;

  ZegoMediaPlayerPlayingProgressEvent(this.mediaPlayer, this.millisecond);

  @override
  String toString() {
    return 'ZegoMediaPlayerPlayingProgressEvent{mediaPlayer: ${mediaPlayer.getIndex()}, millisecond: $millisecond}';
  }
}

class ZegoAudioEffectPlayStateUpdateEvent {
  final int audioEffectID;
  final ZegoAudioEffectPlayState state;
  final int errorCode;

  ZegoAudioEffectPlayStateUpdateEvent(this.audioEffectID, this.state, this.errorCode);

  @override
  String toString() {
    return 'ZegoAudioEffectPlayStateUpdateEvent{audioEffectID: $audioEffectID, state: ${state.name}, errorCode: $errorCode}';
  }
}

class ZegoNetworkSpeedTestQualityUpdateEvent {
  final ZegoStreamQualityLevel quality;
  final ZegoNetworkSpeedTestType type;

  ZegoNetworkSpeedTestQualityUpdateEvent(this.quality, this.type);

  @override
  String toString() {
    return 'ZegoNetworkSpeedTestQualityUpdateEvent{quality: ${quality.toString()}, type: ${type.name}}';
  }
}

class ZegoNetworkSpeedTestErrorEvent {
  final int errorCode;
  final ZegoNetworkSpeedTestType type;

  ZegoNetworkSpeedTestErrorEvent(this.errorCode, this.type);

  @override
  String toString() {
    return 'ZegoNetworkSpeedTestErrorEvent{errorCode: $errorCode, type: ${type.name}}';
  }
}

class ZegoCapturedSoundLevelUpdateEvent {
  final double soundLevel;

  ZegoCapturedSoundLevelUpdateEvent(this.soundLevel);

  @override
  String toString() {
    return 'ZegoCapturedSoundLevelUpdateEvent{soundLevel: $soundLevel}';
  }
}

class ZegoRemoteSoundLevelUpdateEvent {
  final Map<String, double> soundLevels;

  ZegoRemoteSoundLevelUpdateEvent(this.soundLevels);

  @override
  String toString() {
    return 'ZegoRemoteSoundLevelUpdateEvent{soundLevels: $soundLevels}';
  }
}

class ZegoIMRecvBroadcastMessageEvent {
  final String roomID;
  final List<ZegoBroadcastMessageInfo> messageList;

  ZegoIMRecvBroadcastMessageEvent(this.roomID, this.messageList);

  @override
  String toString() {
    return 'ZegoIMRecvBroadcastMessageEvent{roomID: $roomID, messageList: ${messageList.map((e) => '${e.message}(${e.messageID}),')}}';
  }
}

class ZegoIMRecvBarrageMessageEvent {
  final String roomID;
  final List<ZegoBarrageMessageInfo> messageList;

  ZegoIMRecvBarrageMessageEvent(this.roomID, this.messageList);

  @override
  String toString() {
    return 'ZegoIMRecvBarrageMessageEvent{roomID: $roomID, messageList: ${messageList.map((e) => '${e.message}(${e.messageID}),')}}';
  }
}

class ZegoIMRecvCustomCommandEvent {
  final String roomID;
  final ZegoUser fromUser;
  final String command;

  ZegoIMRecvCustomCommandEvent(this.roomID, this.fromUser, this.command);

  @override
  String toString() {
    return 'ZegoIMRecvCustomCommandEvent{roomID: $roomID, fromUser: ${fromUser.userID}(${fromUser.userName}), command: $command}';
  }
}

class ZegoIMSendBroadcastMessageResultEvent {
  final String messageID;
  final int errorCode;

  ZegoIMSendBroadcastMessageResultEvent(this.messageID, this.errorCode);

  @override
  String toString() {
    return 'ZegoIMSendBroadcastMessageResultEvent{messageID: $messageID, errorCode: $errorCode}';
  }
}

class ZegoIMSendBarrageMessageResultEvent {
  final String messageID;
  final int errorCode;

  ZegoIMSendBarrageMessageResultEvent(this.messageID, this.errorCode);

  @override
  String toString() {
    return 'ZegoIMSendBarrageMessageResultEvent{messageID: $messageID, errorCode: $errorCode}';
  }
}

class ZegoIMSendCustomCommandResultEvent {
  final String command;
  final int errorCode;

  ZegoIMSendCustomCommandResultEvent(this.command, this.errorCode);

  @override
  String toString() {
    return 'ZegoIMSendCustomCommandResultEvent{command: $command, errorCode: $errorCode}';
  }
}

class ZegoRoomTokenWillExpireEvent {
  final String roomID;
  final int remainTimeInSecond;

  ZegoRoomTokenWillExpireEvent(this.roomID, this.remainTimeInSecond);

  @override
  String toString() {
    return 'ZegoRoomTokenWillExpireEvent{roomID: $roomID, remainTimeInSecond: $remainTimeInSecond}';
  }
}

class ZegoPublisherUpdateStreamExtraInfoResultEvent {
  final int errorCode;

  ZegoPublisherUpdateStreamExtraInfoResultEvent(this.errorCode);

  @override
  String toString() {
    return 'ZegoPublisherUpdateStreamExtraInfoResultEvent{errorCode: $errorCode}';
  }
}

class ZegoPlayerRenderVideoFirstFrameEvent {
  final String streamID;

  ZegoPlayerRenderVideoFirstFrameEvent(this.streamID);

  @override
  String toString() {
    return 'ZegoPlayerRenderVideoFirstFrameEvent{streamID: $streamID}';
  }
}

class ZegoPlayerRenderAudioFirstFrameEvent {
  final String streamID;

  ZegoPlayerRenderAudioFirstFrameEvent(this.streamID);

  @override
  String toString() {
    return 'ZegoPlayerRenderAudioFirstFrameEvent{streamID: $streamID}';
  }
}

class ZegoPlayerVideoSizeChangedEvent {
  final String streamID;
  final int width;
  final int height;

  ZegoPlayerVideoSizeChangedEvent(this.streamID, this.width, this.height);

  @override
  String toString() {
    return 'ZegoPlayerVideoSizeChangedEvent{streamID: $streamID, width: $width, height: $height}';
  }
}

class ZegoPublisherRenderVideoFirstFrameEvent {
  final ZegoPublishChannel channel;

  ZegoPublisherRenderVideoFirstFrameEvent(this.channel);

  @override
  String toString() {
    return 'ZegoPublisherRenderVideoFirstFrameEvent{channel: ${channel.name}}';
  }
}

class ZegoPublisherVideoSizeChangedEvent {
  final ZegoPublishChannel channel;
  final int width;
  final int height;

  ZegoPublisherVideoSizeChangedEvent(this.channel, this.width, this.height);

  @override
  String toString() {
    return 'ZegoPublisherVideoSizeChangedEvent{channel: ${channel.name}, width: $width, height: $height}';
  }
}

class ZegoPublisherRelayCDNStateUpdateEvent {
  final String streamID;
  final List<ZegoStreamRelayCDNInfo> infoList;

  ZegoPublisherRelayCDNStateUpdateEvent(this.streamID, this.infoList);

  @override
  String toString() {
    return 'ZegoPublisherRelayCDNStateUpdateEvent{streamID: $streamID, infoList: ${infoList.map((e) => '${e.url}(${e.state.name}),')}}';
  }
}

class ZegoEngineStateUpdateEvent {
  final ZegoEngineState state;

  ZegoEngineStateUpdateEvent(this.state);

  @override
  String toString() {
    return 'ZegoEngineStateUpdateEvent{state: ${state.name}}';
  }
}

class ZegoNetworkModeChangedEvent {
  final ZegoNetworkMode mode;

  ZegoNetworkModeChangedEvent(this.mode);

  @override
  String toString() {
    return 'ZegoNetworkModeChangedEvent{mode: ${mode.name}}';
  }
}

class ZegoNetworkQualityEvent {
  final String userID;
  final ZegoStreamQualityLevel upstream;
  final ZegoStreamQualityLevel downstream;

  ZegoNetworkQualityEvent(this.userID, this.upstream, this.downstream);

  @override
  String toString() {
    return 'ZegoNetworkQualityEvent{userID: $userID, upstream: ${upstream.name}, downstream: ${downstream.name}}';
  }
}

class ZegoAudioRouteChangeEvent {
  final ZegoAudioRoute audioRoute;

  ZegoAudioRouteChangeEvent(this.audioRoute);

  @override
  String toString() {
    return 'ZegoAudioRouteChangeEvent{audioRoute: ${audioRoute.name}}';
  }
}

class ZegoPerformanceStatusUpdateEvent {
  final ZegoPerformanceStatus status;

  ZegoPerformanceStatusUpdateEvent(this.status);

  @override
  String toString() {
    return 'ZegoPerformanceStatusUpdateEvent{status: ${status.toString()}}';
  }
}

class ZegoDeviceErrorEvent {
  final int errorCode;
  final String deviceName;

  ZegoDeviceErrorEvent(this.errorCode, this.deviceName);

  @override
  String toString() {
    return 'ZegoDeviceErrorEvent{errorCode: $errorCode, deviceName: $deviceName}';
  }
}

class ZegoRemoteCameraStateUpdateEvent {
  final String streamID;
  final ZegoRemoteDeviceState state;

  ZegoRemoteCameraStateUpdateEvent(this.streamID, this.state);

  @override
  String toString() {
    return 'ZegoRemoteCameraStateUpdateEvent{streamID: $streamID, state: ${state.name}}';
  }
}

class ZegoRemoteMicStateUpdateEvent {
  final String streamID;
  final ZegoRemoteDeviceState state;

  ZegoRemoteMicStateUpdateEvent(this.streamID, this.state);

  @override
  String toString() {
    return 'ZegoRemoteMicStateUpdateEvent{streamID: $streamID, state: ${state.name}}';
  }
}

class ZegoRemoteSpeakerStateUpdateEvent {
  final String streamID;
  final ZegoRemoteDeviceState state;

  ZegoRemoteSpeakerStateUpdateEvent(this.streamID, this.state);

  @override
  String toString() {
    return 'ZegoRemoteSpeakerStateUpdateEvent{streamID: $streamID, state: ${state.name}}';
  }
}

class ZegoAudioDeviceStateChangedEvent {
  final ZegoUpdateType updateType;
  final ZegoAudioDeviceType deviceType;
  final ZegoDeviceInfo deviceInfo;

  ZegoAudioDeviceStateChangedEvent(this.updateType, this.deviceType, this.deviceInfo);

  @override
  String toString() {
    return 'ZegoAudioDeviceStateChangedEvent{updateType: ${updateType.name}, deviceType: ${deviceType.name}, deviceInfo: ${deviceInfo.toString()}}';
  }
}

class ZegoVideoDeviceStateChangedEvent {
  final ZegoUpdateType updateType;
  final ZegoDeviceInfo deviceInfo;

  ZegoVideoDeviceStateChangedEvent(this.updateType, this.deviceInfo);

  @override
  String toString() {
    return 'ZegoVideoDeviceStateChangedEvent{updateType: ${updateType.name}, deviceInfo: ${deviceInfo.toString()}}';
  }
}

class ZegoAudioDeviceVolumeChangedEvent {
  final ZegoAudioDeviceType deviceType;
  final String deviceID;
  final int volume;

  ZegoAudioDeviceVolumeChangedEvent(this.deviceType, this.deviceID, this.volume);

  @override
  String toString() {
    return 'ZegoAudioDeviceVolumeChangedEvent{deviceType: ${deviceType.name}, deviceID: $deviceID, volume: $volume}';
  }
}

class ZegoVideoDeviceExceptionOccurredEvent {
  final ZegoDeviceExceptionType exceptionType;
  final String deviceID;

  ZegoVideoDeviceExceptionOccurredEvent(this.exceptionType, this.deviceID);

  @override
  String toString() {
    return 'ZegoVideoDeviceExceptionOccurredEvent{exceptionType: ${exceptionType.name}, deviceID: $deviceID}';
  }
}

class ZegoCapturedDataRecordStateUpdateEvent {
  final ZegoDataRecordState state;
  final int errorCode;
  final ZegoDataRecordConfig config;
  final ZegoPublishChannel channel;

  ZegoCapturedDataRecordStateUpdateEvent(this.state, this.errorCode, this.config, this.channel);

  @override
  String toString() {
    return 'ZegoCapturedDataRecordStateUpdateEvent{state: ${state.name}, errorCode: $errorCode, config: ${config.toString()}, channel: ${channel.name}}';
  }
}

class ZegoCapturedDataRecordProgressUpdateEvent {
  final ZegoDataRecordProgress progress;
  final ZegoDataRecordConfig config;
  final ZegoPublishChannel channel;

  ZegoCapturedDataRecordProgressUpdateEvent(this.progress, this.config, this.channel);

  @override
  String toString() {
    return 'ZegoCapturedDataRecordProgressUpdateEvent{progress: ${progress.toString()}, config: ${config.toString()}, channel: ${channel.name}}';
  }
}

class ZegoProcessCapturedAudioDataEvent {
  final Uint8List data;
  final int dataLength;
  final ZegoAudioFrameParam param;

  ZegoProcessCapturedAudioDataEvent(this.data, this.dataLength, this.param);

  @override
  String toString() {
    return 'ZegoProcessCapturedAudioDataEvent{dataLength: $dataLength, param: ${param.toString()}}';
  }
}

class ZegoProcessCapturedVideoFrameEvent {
  final int textureID;
  final int width;
  final int height;
  final double timestamp;
  final ZegoPublishChannel channel;

  ZegoProcessCapturedVideoFrameEvent(this.textureID, this.width, this.height, this.timestamp, this.channel);

  @override
  String toString() {
    return 'ZegoProcessCapturedVideoFrameEvent{textureID: $textureID, width: $width, height: $height, timestamp: $timestamp, channel: ${channel.name}}';
  }
}

class ZegoProcessRemoteVideoFrameEvent {
  final String streamID;
  final int textureID;
  final int width;
  final int height;
  final double timestamp;

  ZegoProcessRemoteVideoFrameEvent(this.streamID, this.textureID, this.width, this.height, this.timestamp);

  @override
  String toString() {
    return 'ZegoProcessRemoteVideoFrameEvent{streamID: $streamID, textureID: $textureID, width: $width, height: $height, timestamp: $timestamp}';
  }
}

class ZegoProcessPlaybackAudioDataEvent {
  final Uint8List data;
  final int dataLength;
  final ZegoAudioFrameParam param;

  ZegoProcessPlaybackAudioDataEvent(this.data, this.dataLength, this.param);

  @override
  String toString() {
    return 'ZegoProcessPlaybackAudioDataEvent{dataLength: $dataLength, param: ${param.toString()}}';
  }
}

class ZegoProcessMixedAudioDataEvent {
  final Uint8List data;
  final int dataLength;
  final ZegoAudioFrameParam param;

  ZegoProcessMixedAudioDataEvent(this.data, this.dataLength, this.param);

  @override
  String toString() {
    return 'ZegoProcessMixedAudioDataEvent{dataLength: $dataLength, param: ${param.toString()}}';
  }
}

class ZegoProcessAlignedAudioDataEvent {
  final Uint8List data;
  final int dataLength;
  final ZegoAudioFrameParam param;

  ZegoProcessAlignedAudioDataEvent(this.data, this.dataLength, this.param);

  @override
  String toString() {
    return 'ZegoProcessAlignedAudioDataEvent{dataLength: $dataLength, param: ${param.toString()}}';
  }
}

class RoomRequest {
  String requestID;
  String sender;
  String receiver;
  String extendedData;
  int createTime;
  int acceptTime;
  int cancelTime;
  int rejectTime;
  int expireTime;
  int state;

  RoomRequest({
    required this.requestID,
    required this.sender,
    required this.receiver,
    required this.extendedData,
    required this.createTime,
    required this.acceptTime,
    required this.cancelTime,
    required this.rejectTime,
    required this.expireTime,
    required this.state,
  });

  factory RoomRequest.fromJson(Map<String, dynamic> json) {
    return RoomRequest(
      requestID: json['id'],
      sender: json['sender_id'],
      receiver: json['receiver_id'],
      extendedData: json['extended_data'],
      createTime: json['create_time'],
      acceptTime: json['accept_time'],
      cancelTime: json['cancel_time'],
      rejectTime: json['reject_time'],
      expireTime: json['expire_time'],
      state: json['state'],
    );
  }

  Map<String, dynamic> toJson() => {
        'id': requestID,
        'sender_id': sender,
        'receiver_id': receiver,
        'extended_data': extendedData,
        'create_time': createTime,
        'accept_time': acceptTime,
        'cancel_time': cancelTime,
        'reject_time': rejectTime,
        'expire_time': expireTime,
        'state': state,
      };

  @override
  String toString() {
    return 'RoomRequest{requestID: $requestID, sender: $sender, receiver: $receiver, extendedData: $extendedData, createTime: $createTime, acceptTime: $acceptTime, cancelTime: $cancelTime, rejectTime: $rejectTime, expireTime: $expireTime, state: $state}';
  }
}

enum RoomRequestState {
  untreated,
  accepted,
  rejected,
  timeout,
  canceled,
}

enum RoomRequestType {
  audienceApplyToBecomeCoHost,
  hostInviteAudienceToBecomeCoHost,
  removeCoHost,
  audienceCancelCoHostApply,
  hostCancelCoHostInvitation,
}

class SendRoomRequestEvent {
  final String requestID;
  String? extendedData;
  SendRoomRequestEvent({required this.requestID, this.extendedData});

  @override
  String toString() {
    return 'SendRoomRequestEvent{requestID: $requestID, extendedData: $extendedData}';
  }
}

class AcceptRoomRequestEvent {
  final String requestID;
  String? extendedData;
  AcceptRoomRequestEvent({required this.requestID, this.extendedData});

  @override
  String toString() {
    return 'AcceptRoomRequestEvent{requestID: $requestID, extendedData: $extendedData}';
  }
}

class RejectRoomRequestEvent {
  final String requestID;
  String? extendedData;
  RejectRoomRequestEvent({required this.requestID, this.extendedData});

  @override
  String toString() {
    return 'RejectRoomRequestEvent{requestID: $requestID, extendedData: $extendedData}';
  }
}

class CancelRoomRequestEvent {
  final String requestID;
  String? extendedData;
  CancelRoomRequestEvent({required this.requestID, this.extendedData});

  @override
  String toString() {
    return 'CancelRoomRequestEvent{requestID: $requestID, extendedData: $extendedData}';
  }
}

class OnInComingRoomRequestReceivedEvent {
  final String requestID;
  String? extendedData;
  OnInComingRoomRequestReceivedEvent({required this.requestID, this.extendedData});

  @override
  String toString() {
    return 'OnInComingRoomRequestReceivedEvent{requestID: $requestID, extendedData:$extendedData}';
  }
}

class OnOutgoingRoomRequestAcceptedEvent {
  final String requestID;
  String? extendedData;
  OnOutgoingRoomRequestAcceptedEvent({required this.requestID, this.extendedData});

  @override
  String toString() {
    return 'OnOutgoingRoomRequestAcceptedEvent{requestID: $requestID, extendedData:$extendedData}';
  }
}

class OnOutgoingRoomRequestRejectedEvent {
  final String requestID;
  String? extendedData;
  OnOutgoingRoomRequestRejectedEvent({required this.requestID, this.extendedData});

  @override
  String toString() {
    return 'OnOutgoingRoomRequestRejectedEvent{requestID: $requestID, extendedData:$extendedData}';
  }
}

class OnInComingRoomRequestCancelledEvent {
  final String requestID;
  String? extendedData;
  OnInComingRoomRequestCancelledEvent({required this.requestID, this.extendedData});

  @override
  String toString() {
    return 'OnInComingRoomRequestCancelledEvent{requestID: $requestID, extendedData:$extendedData}';
  }
}

class OnRoomCommandReceivedEvent {
  final String senderID;
  final String command;

  OnRoomCommandReceivedEvent(this.senderID, this.command);

  @override
  String toString() {
    return 'OnRoomCommandReceivedEvent{senderID: $senderID command:$command}';
  }
}

class ButtonIcon {
  Widget? icon;
  String? normalIconUrl;
  String? selectedIconUrl;
  Color? backgroundColor;
  Color? borderColor;
  double? borderWidth;
  double? borderRadius;

  ButtonIcon({
    this.icon,
    this.normalIconUrl,
    this.selectedIconUrl,
    this.backgroundColor,
    this.borderColor,
    this.borderWidth,
    this.borderRadius,
  });
}
