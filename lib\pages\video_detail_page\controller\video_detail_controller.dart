import 'dart:developer';
import 'package:get/get.dart';
import 'package:shortie/pages/reels_page/api/fetch_reels_api.dart';
import 'package:shortie/pages/reels_page/model/fetch_reels_model.dart';
import 'package:shortie/pages/video_detail_page/api/fetch_video_by_id_api.dart';
import 'package:shortie/utils/database.dart';
import 'package:shortie/utils/utils.dart';

class VideoDetailController extends GetxController {
  String videoId = "";
  bool fromDeepLink = false;
  bool isLoading = true;
  
  FetchReelsModel? fetchReelsModel;
  List<Data> videoList = [];
  int currentVideoIndex = 0;

  @override
  void onInit() {
    final arguments = Get.arguments;
    if (arguments != null) {
      videoId = arguments['videoId'] ?? "";
      fromDeepLink = arguments['fromDeepLink'] ?? false;
    }
    
    Utils.showLog("Video Detail Page - Video ID: $videoId, From Deep Link: $fromDeepLink");
    
    if (videoId.isNotEmpty) {
      onGetVideoDetails();
    } else {
      Utils.showLog("Error: No video ID provided");
      Get.back();
    }
    
    super.onInit();
  }

  Future<void> onGetVideoDetails() async {
    try {
      isLoading = true;
      update(["onGetVideoDetails"]);

      // Fetch the specific video and related videos
      fetchReelsModel = await FetchReelsApi.callApi(loginUserId: Database.loginUserId, videoId: "");

      if (fetchReelsModel?.data != null) {
        videoList = fetchReelsModel?.data ?? [];
        
        // Find the specific video by ID
        int foundIndex = videoList.indexWhere((video) => video.id == videoId);
        
        if (foundIndex != -1) {
          currentVideoIndex = foundIndex;
          Utils.showLog("Found video at index: $currentVideoIndex");
        } else {
          // If specific video not found, try to fetch it individually
          Utils.showLog("Video not found in main feed, attempting individual fetch");
          await onFetchIndividualVideo();
        }
      }

      isLoading = false;
      update(["onGetVideoDetails"]);
      
    } catch (e) {
      Utils.showLog("Error fetching video details: $e");
      isLoading = false;
      update(["onGetVideoDetails"]);
      
      // Show error and go back
      Get.snackbar(
        "Error", 
        "Failed to load video. Please try again.",
        snackPosition: SnackPosition.BOTTOM,
      );
      Get.back();
    }
  }

  Future<void> onFetchIndividualVideo() async {
    try {
      Utils.showLog("Fetching individual video with ID: $videoId");

      // Try to fetch the specific video by ID
      Data? individualVideo = await FetchVideoByIdApi.callApi(
        loginUserId: Database.loginUserId,
        videoId: videoId,
      );

      if (individualVideo != null) {
        // Add the individual video to the beginning of the list
        videoList.insert(0, individualVideo);
        currentVideoIndex = 0;
        Utils.showLog("Individual video fetched successfully");
      } else {
        // Fallback: if video not found, show first video or go back
        if (videoList.isNotEmpty) {
          currentVideoIndex = 0;
          Utils.showLog("Using fallback - showing first available video");
        } else {
          throw Exception("Video not found and no fallback videos available");
        }
      }

    } catch (e) {
      Utils.showLog("Error fetching individual video: $e");
      throw e;
    }
  }

  void onVideoChanged(int index) {
    currentVideoIndex = index;
    update(["onVideoChanged"]);
  }

  @override
  void onClose() {
    Utils.showLog("Video Detail Controller disposed");
    super.onClose();
  }
}
