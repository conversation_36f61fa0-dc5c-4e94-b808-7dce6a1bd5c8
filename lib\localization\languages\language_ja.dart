import 'package:shortie/utils/enums.dart';

final Map<String, String> jaJP = {
  EnumLocal.txtAppName.name: "ショーティ",
  EnumLocal.txtSkip.name: "スキップ",
  EnumLocal.txtOnBoardingTitle_1.name: "今世紀最高のソーシャルメディアアプリ",
  EnumLocal.txtOnBoardingTitle_2.name: "世界中のみんなとつながろう",
  EnumLocal.txtOnBoardingTitle_3.name: "アプリでできることすべて",
  EnumLocal.txtOnBoardingSubTitle_1.name: "最先端の機能と直感的なデザインでソーシャル メディアの未来を体験してください。これまでにない方法でつながり、共有し、発見する準備をしましょう。",
  EnumLocal.txtOnBoardingSubTitle_2.name: "情熱的で多様性があり、活気に満ちたグローバル コミュニティに参加しましょう。他の人とつながり、あなたのストーリーを共有し、世界をより小さく、より素晴らしい場所にしましょう。",
  EnumLocal.txtOnBoardingSubTitle_3.name: "人生の瞬間の共有から新しい興味の発見まで、私たちのアプリはあなたをサポートします。無限の可能性の世界で探索し、創造し、繁栄しましょう!",
  EnumLocal.txtLoginTitle.name: "あなたの周りの新しい人に会いましょう",
  EnumLocal.txtLoginSubTitle.name: "いらっしゃいませ！すぐにサインインするか、Google アカウントを使用して、周りの新しい人との出会いを始めましょう。新しい友情への旅を始めましょう！",
  EnumLocal.txtQuickLogIn.name: "クイックログイン",
  EnumLocal.txtContinueWithGoogle.name: "Google を続ける",
  EnumLocal.txtFillProfile.name: "プロファイルを埋める",
  EnumLocal.txtEditProfile.name: "プロファイル編集",
  EnumLocal.txtFullName.name: "フルネーム",
  EnumLocal.txtUserName.name: "ユーザー名",
  EnumLocal.txtIdentificationCode.name: "認証コード",
  EnumLocal.txtCountry.name: "国",
  EnumLocal.txtBioDetails.name: "略歴の詳細",
  EnumLocal.txtGender.name: "性別",
  EnumLocal.txtMale.name: "男",
  EnumLocal.txtFemale.name: "女性",
  EnumLocal.txtOther.name: "他の",
  EnumLocal.txtSaveProfile.name: "プロファイルを保存",
  EnumLocal.txtSendGift.name: "ギフトを送る",
  EnumLocal.txtSend.name: "送信",
  EnumLocal.txtCoins.name: "コイン",
  EnumLocal.txtRecharge.name: "リチャージ",
  EnumLocal.txtComment.name: "コメント",
  EnumLocal.txtTypeComment.name: "コメントを入力...",
  EnumLocal.txtVideos.name: "動画",
  EnumLocal.txtNext.name: "次",
  EnumLocal.txtImages.name: "画像",
  EnumLocal.txtAddMusic.name: "音楽を追加する",
  EnumLocal.txtSearchHintText.name: "検索...",
  EnumLocal.txtDiscover.name: "発見する",
  EnumLocal.txtFavourite.name: "お気に入り",
  EnumLocal.txtPreview.name: "プレビュー",
  EnumLocal.txtUploadReels.name: "リールをアップロードする",
  EnumLocal.txtWhatsOnYourMind.name: "何を考えているの？",
  EnumLocal.txtChangeThumbnail.name: "サムネイルの変更",
  EnumLocal.txtAddTopic.name: "トピックを追加",
  EnumLocal.txtUpload.name: "アップロード",
  EnumLocal.txtAddHashtag.name: "ハッシュタグを追加する",
  EnumLocal.txtDone.name: "終わり",
  EnumLocal.txtLiveStreaming.name: "生放送",
  EnumLocal.txtGoLive.name: "ライブに移行",
  EnumLocal.txtLive.name: "ライブ",
  EnumLocal.txtStopLive.name: "ライブを停止",
  EnumLocal.txtStop.name: "停止",
  EnumLocal.txtStopLiveDialogText.name: "ライブストリームを停止しますか?この操作は元に戻すことができず、ブロードキャストは直ちに終了します。 「ストリーミングを停止」をクリックして確認します。",
  EnumLocal.txtFeeds.name: "フィード",
  EnumLocal.txtCreate.name: "作成する",
  EnumLocal.txtSayHi.name: "「こんにちは」と言ってください",
  EnumLocal.txtReport.name: "報告",
  EnumLocal.txtCancel.name: "キャンセル",
  EnumLocal.txtItIsSpam.name: "スパムです",
  EnumLocal.txtNudityOrSexualActivity.name: "ヌードまたは性的行為",
  EnumLocal.txtHateSpeechOrSymbols.name: "ヘイトスピーチやシンボル",
  EnumLocal.txtViolenceOrDangerousOrganization.name: "暴力や危険な組織",
  EnumLocal.txtFalseInformation.name: "虚偽の情報",
  EnumLocal.txtBullyingOrHarassment.name: "いじめや嫌がらせ",
  EnumLocal.txtScamOrFraud.name: "詐欺または詐欺",
  EnumLocal.txtIntellectualPropertyViolation.name: "知的財産権の侵害",
  EnumLocal.txtSuicideOrSelfInjury.name: "自殺または自傷行為",
  EnumLocal.txtDrugs.name: "薬物",
  EnumLocal.txtEatingDisorders.name: "摂食障害",
  EnumLocal.txtSomethingElse.name: "何か他のもの",
  EnumLocal.txtChildAbuse.name: "児童虐待",
  EnumLocal.txtOthers.name: "その他",
  EnumLocal.txtUploadPost.name: "投稿をアップロードする",
  EnumLocal.txtSearch.name: "検索",
  EnumLocal.txtHashTag.name: "ハッシュタグ",
  EnumLocal.txtViewAll.name: "すべて見る",
  EnumLocal.txtScanQRCode.name: "QRコードをスキャン",
  EnumLocal.txtScanQRCodeText.name: "QRコードをスキャンしてプロフィールをご覧ください",
  EnumLocal.txtMyQRCodeText.name: "私のQRコードページへようこそ！上の QR コードをスキャンすると、すぐに私のプロフィール データにアクセスできます。これには、私の連絡先情報、ソーシャル メディア プロフィール、および私と簡単につながるのに役立つその他の重要な詳細が含まれます。スマートフォンのカメラを QR コードにかざすだけで、私の個人的なプロフィール ページに移動します。接続を維持し、簡単にスキャンするだけで私についてもっと知ることができます。",
  EnumLocal.txtViewProfile.name: "プロフィールを見る",
  EnumLocal.txtViewDetails.name: "詳細を見る",
  EnumLocal.txtFollow.name: "フォローする",
  EnumLocal.txtFollowing.name: "続く",
  EnumLocal.txtLikes.name: "いいね！",
  EnumLocal.txtFollowers.name: "フォロワー",
  EnumLocal.txtReels.name: "リール",
  EnumLocal.txtCollections.name: "コレクション",
  EnumLocal.txtSettings.name: "設定",
  EnumLocal.txtAccount.name: "アカウント",
  EnumLocal.txtNotifyMe.name: "私に通知する",
  EnumLocal.txtLanguages.name: "言語",
  EnumLocal.txtMyWallet.name: "私の財布",
  EnumLocal.txtShareProfile.name: "プロフィールを共有する",
  EnumLocal.txtMyQRCode.name: "私のQRコード",
  EnumLocal.txtVerificationRequest.name: "検証リクエスト",
  EnumLocal.txtGeneral.name: "一般的な",
  EnumLocal.txtHelp.name: "ヘルプ",
  EnumLocal.txtTermsOfUse.name: "利用規約",
  EnumLocal.txtPrivacyPolicy.name: "プライバシーポリシー",
  EnumLocal.txtLogOut.name: "ログアウト",
  EnumLocal.txtDeleteAccount.name: "アカウントを削除する",
  EnumLocal.txtDelete.name: "消去",
  EnumLocal.txtLogOutText.name: "アカウントからログアウトしようとしています。ログインページにリダイレクトされます。続行してもよろしいですか?",
  EnumLocal.txtDeleteAccountText.name: "アカウントを完全に削除してもよろしいですか?この操作は元に戻すことができず、すべてのデータが失われ、関連サービスにアクセスできなくなります。",
  EnumLocal.txtUploadYourImages.name: "画像をアップロードする",
  EnumLocal.txtUploadIDPhotos.name: "証明写真をアップロードする",
  EnumLocal.txtPersonalPhotos.name: "個人的な写真",
  EnumLocal.txtClearPhotos.name: "鮮明な写真",
  EnumLocal.txtCapture.name: "捕獲",
  EnumLocal.txtAttach.name: "添付する",
  EnumLocal.txtIDNumber.name: "ID番号",
  EnumLocal.txtNameOnID.name: "ID上の名前",
  EnumLocal.txtFullAddress.name: "完全な住所",
  EnumLocal.txtSubmit.name: "提出する",
  EnumLocal.txtAvailableCoinBalance.name: "利用可能なコイン残高",
  EnumLocal.txtRechargeCoin.name: "コインをチャージする",
  EnumLocal.txtCoinHistory.name: "コインの歴史",
  EnumLocal.txtWithdraw.name: "撤回する",
  EnumLocal.txtAvailableCoin.name: "利用可能なコイン",
  EnumLocal.txtEnterCoin.name: "コインを入力してください",
  EnumLocal.txtEnterWithdrawCoin.name: "出金コインを入力してください...",
  EnumLocal.txtMinimumWithdraw.name: "*最低出金額",
  EnumLocal.txtSelectPaymentGateway.name: "支払いゲートウェイを選択してください",
  EnumLocal.txtSelectPaymentMethod.name: "支払い方法を選択してください",
  EnumLocal.txtPurchasePremiumPlanAndGetAllAccess.name: "プレミアム プランを購入してすべてのアクセスを取得",
  EnumLocal.txtRechargePageSubTitle.name: "プレミアム プランにアップグレードすると、高度なツールや限定リソースに無制限にアクセスできます。今すぐあなたのエクスペリエンスを高めましょう。",
  EnumLocal.txtConfirmWithdrawDialogText.name: "正確性を確保するために、引き出しの詳細を注意深く確認してください。間違いがないよう、宛先口座情報と出金金額を再度ご確認ください。",
  EnumLocal.txtPurchasePlan.name: "購入プラン",
  EnumLocal.txtPayNow.name: "今払う",
  EnumLocal.txtMostPopularPlan.name: "一番人気のプラン",
  EnumLocal.txtComplaintOrSuggestion.name: "苦情または提案",
  EnumLocal.txtTypingSomethings.name: "何かを入力しています...",
  EnumLocal.txtContact.name: "接触",
  EnumLocal.txtAttachYourImageOrScreenshot.name: "画像またはスクリーンショットを添付してください",
  EnumLocal.txtBrowse.name: "ブラウズ",
  EnumLocal.txtNone.name: "なし",
  EnumLocal.txtChooseImage.name: "画像の選択",
  EnumLocal.txtGallery.name: "ギャラリー",
  EnumLocal.txtTakePhoto.name: "写真を撮る",
  EnumLocal.txtChooseVideo.name: "ビデオを選択",
  EnumLocal.txtCreateReels.name: "リールの作成",
  EnumLocal.txtNoDataFound.name: "何もデータが見つかりませんでした ！！",
  EnumLocal.txtTypeSomething.name: "何かを入力...",
  EnumLocal.txtSomeThingWentWrong.name: "何か問題が発生しました!!",
  EnumLocal.txtConnectionLost.name: "接続切断 ！！",
  EnumLocal.txtNoInternetConnection.name: "インターネット接続がありません!!",
  EnumLocal.txtLoginSuccess.name: "ログイン成功",
  EnumLocal.txtProfileUpdateSuccessfully.name: "プロファイルが正常に更新されました",
  EnumLocal.txtReelsUploadSuccessfully.name: "リールが正常にアップロードされました",
  EnumLocal.txtYouCantFollowYourOwnAccount.name: "自分のアカウントをフォローすることはできません",
  EnumLocal.txtReportSending.name: "レポートを送信中...",
  EnumLocal.txtReportSendSuccess.name: "レポート送信成功",
  EnumLocal.txtYouCanSelectMaximumFiveImages.name: "画像は最大5枚まで選択可能!!",
  EnumLocal.txtPostUploading.name: "アップロード後...",
  EnumLocal.txtPostUploadSuccessfully.name: "アップロードが正常に完了しました",
  EnumLocal.txtPleaseSelectProfileImage.name: "プロフィール画像を選択してください!!",
  EnumLocal.txtPleaseEnterFullName.name: "フルネームを入力してください!!",
  EnumLocal.txtPleaseEnterUserName.name: "ユーザー名を入力してください!!",
  EnumLocal.txtPleaseEnterBioDetails.name: "プロフィール詳細を入力してください!!",
  EnumLocal.txtPleaseAllowPermission.name: "許可をお願いします!!",
  EnumLocal.txtPleaseWaitSomeTime.name: "しばらくお待ちください...",
  EnumLocal.txtPleaseEnterCaption.name: "キャプションを入力してください!!",
  EnumLocal.txtPleaseSelectHashTag.name: "ハッシュタグを選択してください!!",
  EnumLocal.txtPleaseSelectPost.name: "ポストを選択してください!!",
  EnumLocal.txtPleaseEnterWithdrawCoin.name: "出金コインを入力してください!!",
  EnumLocal.txtPleaseSelectWithdrawMethod.name: "出金方法を選択してください!!",
  EnumLocal.txtPleaseEnterAllPaymentDetails.name: "すべての支払い詳細を入力してください!!",
  EnumLocal.txtCoinNotAvailableForSendGiftText.name: "ギフトを送るのに十分な資金がありません!!",
  EnumLocal.txtWithdrawalRequestedCoinMustBeGreaterThanSpecifiedByTheAdmin.name: "出金要求コインは管理者が指定したコインより大きくなければなりません!!",
  EnumLocal.txtDownloadSuccess.name: "ダウンロード成功",
  EnumLocal.txtPleaseEnterYourComplain.name: "苦情を入力してください!!",
  EnumLocal.txtPleaseEnterYourContactNumber.name: "連絡先番号を入力してください!!",
  EnumLocal.txtPleaseUploadScreenShort.name: "画面を短くアップロードしてください!!",
  EnumLocal.txtYourComplainSendSuccessfully.name: "苦情は正常に送信されました",
  EnumLocal.txtCoinRechargeSuccess.name: "コインリチャージ成功",
  EnumLocal.txtPleaseUploadProfileImage.name: "プロフィール画像をアップロードしてください!!",
  EnumLocal.txtPleaseUploadDocumentImage.name: "資料画像をアップロードしてください!!",
  EnumLocal.txtPleaseEnterYourIdOnNumber.name: "番号にIDを入力してください!!",
  EnumLocal.txtPleaseEnterYourIdOnName.name: "名前にIDを入力してください!!",
  EnumLocal.txtPleaseEnterYourIdOnAddress.name: "アドレスにIDを入力してください!!",
  EnumLocal.txtVerificationRequestSendSuccessfully.name: "検証リクエストが正常に送信されました",
  EnumLocal.txtLongPressToEnableAudioRecording.name: "長押しして音声録音を有効にします",
  EnumLocal.txtAudioRecording.name: "録音...",
  EnumLocal.txtOptionalInBrackets.name: "",
  EnumLocal.txtTheUserDoesNotHaveSufficientFundsToMakeTheWithdrawal.name: "",
  EnumLocal.txtVideo.name: "(オプション)",
  EnumLocal.txtPost.name: "ユーザーには出金するのに十分な資金がありません",
  EnumLocal.txtHastTag.name: "ビデオ",
  EnumLocal.txtCaption.name: "役職",
  EnumLocal.txtReceivedCoin.name: "ハッシュタグ",
  EnumLocal.txtSendCoin.name: "キャプション",
  EnumLocal.txtAddWallet.name: "受け取ったコイン",
  EnumLocal.txtWithdrawal.name: "コインを送る",
  EnumLocal.txtPendingWithdrawal.name: "ウォレットの追加",
  EnumLocal.txtCancelWithdrawal.name: "出金",
  EnumLocal.txtTypeYourHashtag.name: "ハッシュタグを入力してください...",
  EnumLocal.txtYouCantSendGiftOwnVideo.name: "自分の動画にギフトを送信することはできません",
  EnumLocal.txtWelcomeBonusCoin.name: "ウェルカムボーナスコイン",
  EnumLocal.txtSendGiftCoin.name: "ギフトコインを送る",
  EnumLocal.txtReceiveGiftCoin.name: "ギフトコインを受け取る",
  EnumLocal.txtYouDonHaveSufficientCoinsToSendTheGift.name: "ギフトを送るのに十分なコインがありません!!",
  EnumLocal.txtEnterYourTextWithHashtag.name: "ハッシュタグを付けて本文を入力してください...",
  EnumLocal.txtDeleteAll.name: "すべて削除",
  EnumLocal.txtRequests.name: "リクエスト",
  EnumLocal.txtDecline.name: "衰退",
  EnumLocal.txtAccept.name: "受け入れる",
  EnumLocal.txtIgnore.name: "無視する",
  EnumLocal.txtAcceptMessageRequestFrom.name: "からのメッセージリクエストを受け入れる",
  EnumLocal.txtUploadYourFirstVideo.name: "からのメッセージリクエストを受け入れる",
  EnumLocal.txtEnterVerificationCode.name: "最初のビデオをアップロードする",
  EnumLocal.txtEnterVerificationCodeAsUnder.name: "\n確認コードを入力してください",
  EnumLocal.txtSendCodeThisNumber.name: "以下のように確認コードを入力してください",
  EnumLocal.txtResendCode.name: "この番号にコードを送信します",
  EnumLocal.EnterMobileNumber.name: "コードを再送信する",
  EnumLocal.txtEnterYourMobileNumberHereAndContinue.name: "携帯電話番号を\n入力してください",
  EnumLocal.txtVerify.name: "ここに携帯電話番号を入力して続行してください...",
  EnumLocal.txtMobileNumHintTest.name: "確認する",
  EnumLocal.txtPleaseEnterCorrectMobileNumber.name: "携帯電話番号を入力してください",
  EnumLocal.txtPleaseEnterMobileNumber.name: "正しい携帯番号を入力してください",
  EnumLocal.txtVerificationTimeout.name: "携帯電話番号を入力してください",
  EnumLocal.txtVerificationCodeSend.name: "検証タイムアウト",
  EnumLocal.txtPleaseEnterVerificationCode.name: "認証コードを送信します。",
  EnumLocal.txtMoreOption.name: "確認コードを入力してください",
  EnumLocal.txtDeletePost.name: "その他のオプション",
  EnumLocal.txtDeleteVideo.name: "投稿の削除",
  EnumLocal.txtDeletePostVideoContent.name: "ビデオの削除",
  EnumLocal.txtMobile.name: "携帯",
  EnumLocal.txtGoogle.name: "グーグル",
  EnumLocal.txtOr.name: "または",
  EnumLocal.txtUseAudio.name: "音声を使用する",
  EnumLocal.txtAudio.name: "オーディオ",
  EnumLocal.txtEditPost.name: "投稿の編集",
  EnumLocal.txtEditReels.name: "リールの編集",
  EnumLocal.txtEdit.name: "編集",
};
