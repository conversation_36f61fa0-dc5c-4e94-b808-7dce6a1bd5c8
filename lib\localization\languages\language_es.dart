import 'package:shortie/utils/enums.dart';

final Map<String, String> esES = {
  EnumLocal.txtAppName.name: "b<PERSON><PERSON>",
  EnumLocal.txtSkip.name: "Saltar",
  EnumLocal.txtOnBoardingTitle_1.name: "La mejor aplicación de redes sociales del siglo",
  EnumLocal.txtOnBoardingTitle_2.name: "Conectémonos con todos en el mundo",
  EnumLocal.txtOnBoardingTitle_3.name: "Todo lo que puedes hacer en la aplicación",
  EnumLocal.txtOnBoardingSubTitle_1.name: "Experimente el futuro de las redes sociales con nuestras funciones de vanguardia y diseño intuitivo. ¡Prepárate para conectarte, compartir y descubrir como nunca antes!",
  EnumLocal.txtOnBoardingSubTitle_2.name: "Únase a una comunidad global apasionada, diversa y vibrante. ¡Conéctate con otros, comparte tu historia y haz del mundo un lugar más pequeño y maravilloso!",
  EnumLocal.txtOnBoardingSubTitle_3.name: "Desde compartir momentos de la vida hasta descubrir nuevos intereses, nuestra aplicación lo tiene cubierto. ¡Explora, crea y prospera en un mundo de infinitas posibilidades!",
  EnumLocal.txtLoginTitle.name: "Conozcamos gente nueva a tu alrededor",
  EnumLocal.txtLoginSubTitle.name: "¡Bienvenido! Inicia sesión rápidamente o usa tu cuenta de Google para comenzar a conocer gente nueva a tu alrededor. ¡Comencemos tu viaje hacia nuevas amistades!",
  EnumLocal.txtQuickLogIn.name: "Inicio de sesión rápido",
  EnumLocal.txtContinueWithGoogle.name: "Continuar con Google",
  EnumLocal.txtFillProfile.name: "Rellenar perfil",
  EnumLocal.txtEditProfile.name: "Editar perfil",
  EnumLocal.txtFullName.name: "Nombre completo",
  EnumLocal.txtUserName.name: "Nombre de usuario",
  EnumLocal.txtIdentificationCode.name: "Código de identificación",
  EnumLocal.txtCountry.name: "País",
  EnumLocal.txtBioDetails.name: "Detalles de la biografía",
  EnumLocal.txtGender.name: "Género",
  EnumLocal.txtMale.name: "Masculino",
  EnumLocal.txtFemale.name: "Femenino",
  EnumLocal.txtOther.name: "Otro",
  EnumLocal.txtSaveProfile.name: "Guardar perfil",
  EnumLocal.txtSendGift.name: "Enviar regalo",
  EnumLocal.txtSend.name: "Enviar",
  EnumLocal.txtCoins.name: "monedas",
  EnumLocal.txtRecharge.name: "Recargar",
  EnumLocal.txtComment.name: "Comentario",
  EnumLocal.txtTypeComment.name: "Escriba comentario...",
  EnumLocal.txtVideos.name: "Vídeos",
  EnumLocal.txtNext.name: "Próximo",
  EnumLocal.txtImages.name: "Imágenes",
  EnumLocal.txtAddMusic.name: "Agregar música",
  EnumLocal.txtSearchHintText.name: "Buscar...",
  EnumLocal.txtDiscover.name: "Descubrir",
  EnumLocal.txtFavourite.name: "Favorito",
  EnumLocal.txtPreview.name: "Avance",
  EnumLocal.txtUploadReels.name: "Subir carretes",
  EnumLocal.txtWhatsOnYourMind.name: "¿Qué tienes en mente?",
  EnumLocal.txtChangeThumbnail.name: "Cambiar miniatura",
  EnumLocal.txtAddTopic.name: "Agregar tema",
  EnumLocal.txtUpload.name: "Subir",
  EnumLocal.txtAddHashtag.name: "Agregar hashtag",
  EnumLocal.txtDone.name: "Hecho",
  EnumLocal.txtLiveStreaming.name: "Transmisión en vivo",
  EnumLocal.txtGoLive.name: "Ir a vivir",
  EnumLocal.txtLive.name: "Vivir",
  EnumLocal.txtStopLive.name: "Detener en vivo",
  EnumLocal.txtStop.name: "Detener",
  EnumLocal.txtStopLiveDialogText.name: "¿Detener la transmisión en vivo? Esta acción no se puede deshacer y finalizará la transmisión inmediatamente. Haga clic en Detener transmisión para confirmar.",
  EnumLocal.txtFeeds.name: "Feeds",
  EnumLocal.txtCreate.name: "Crear",
  EnumLocal.txtSayHi.name: "Di hola",
  EnumLocal.txtReport.name: "Informe",
  EnumLocal.txtCancel.name: "Cancelar",
  EnumLocal.txtItIsSpam.name: "Es spam",
  EnumLocal.txtNudityOrSexualActivity.name: "Desnudez o actividad sexual.",
  EnumLocal.txtHateSpeechOrSymbols.name: "Discurso o símbolos de odio",
  EnumLocal.txtViolenceOrDangerousOrganization.name: "Violencia u organización peligrosa.",
  EnumLocal.txtFalseInformation.name: "Información falsa",
  EnumLocal.txtBullyingOrHarassment.name: "Intimidación o acoso",
  EnumLocal.txtScamOrFraud.name: "Estafa o fraude",
  EnumLocal.txtIntellectualPropertyViolation.name: "Violación de propiedad intelectual",
  EnumLocal.txtSuicideOrSelfInjury.name: "Suicidio o autolesión",
  EnumLocal.txtDrugs.name: "Drogas",
  EnumLocal.txtEatingDisorders.name: "Trastornos de la alimentación",
  EnumLocal.txtSomethingElse.name: "Algo más",
  EnumLocal.txtChildAbuse.name: "Abuso infantil",
  EnumLocal.txtOthers.name: "Otros",
  EnumLocal.txtUploadPost.name: "Subir publicación",
  EnumLocal.txtSearch.name: "Buscar",
  EnumLocal.txtHashTag.name: "Hashtag",
  EnumLocal.txtViewAll.name: "Ver todo",
  EnumLocal.txtScanQRCode.name: "Escanear código QR",
  EnumLocal.txtScanQRCodeText.name: "Escanea el código QR para ver el perfil",
  EnumLocal.txtMyQRCodeText.name: "¡Bienvenido a mi página de códigos QR! Al escanear el código QR de arriba, accederá instantáneamente a los datos de mi perfil. Esto incluye mi información de contacto, perfiles de redes sociales y otros detalles esenciales que te ayudarán a conectarte conmigo sin esfuerzo. Simplemente apunte la cámara de su teléfono inteligente al código QR y será dirigido a mi página de perfil personalizada. ¡Manténgase conectado y aprenda más sobre mí con solo un escaneo rápido!",
  EnumLocal.txtViewProfile.name: "Ver perfil",
  EnumLocal.txtViewDetails.name: "Ver detalles",
  EnumLocal.txtFollow.name: "Seguir",
  EnumLocal.txtFollowing.name: "Siguiente",
  EnumLocal.txtLikes.name: "Gustos",
  EnumLocal.txtFollowers.name: "Seguidores",
  EnumLocal.txtReels.name: "Bobinas",
  EnumLocal.txtCollections.name: "Colecciones",
  EnumLocal.txtSettings.name: "Ajustes",
  EnumLocal.txtAccount.name: "Cuenta",
  EnumLocal.txtNotifyMe.name: "Notificarme",
  EnumLocal.txtLanguages.name: "Idiomas",
  EnumLocal.txtMyWallet.name: "Mi billetera",
  EnumLocal.txtShareProfile.name: "Compartir perfil",
  EnumLocal.txtMyQRCode.name: "Mi código QR",
  EnumLocal.txtVerificationRequest.name: "Solicitud de verificación",
  EnumLocal.txtGeneral.name: "General",
  EnumLocal.txtHelp.name: "Ayuda",
  EnumLocal.txtTermsOfUse.name: "Condiciones de uso",
  EnumLocal.txtPrivacyPolicy.name: "política de privacidad",
  EnumLocal.txtLogOut.name: "Cerrar sesión",
  EnumLocal.txtDeleteAccount.name: "Borrar cuenta",
  EnumLocal.txtDelete.name: "Borrar",
  EnumLocal.txtLogOutText.name: "Estás a punto de cerrar sesión en tu cuenta. Serás redirigido a la página de inicio de sesión. Estás seguro de que quieres continuar?",
  EnumLocal.txtDeleteAccountText.name: "¿Estás seguro de que deseas eliminar permanentemente tu cuenta? Esta acción no se puede deshacer y resultará en la pérdida de todos sus datos y del acceso a los servicios asociados.",
  EnumLocal.txtUploadYourImages.name: "Sube tus imágenes",
  EnumLocal.txtUploadIDPhotos.name: "Subir fotos de identificación",
  EnumLocal.txtPersonalPhotos.name: "Fotos personales",
  EnumLocal.txtClearPhotos.name: "Borrar fotos",
  EnumLocal.txtCapture.name: "Captura",
  EnumLocal.txtAttach.name: "Adjuntar",
  EnumLocal.txtIDNumber.name: "Número de identificación",
  EnumLocal.txtNameOnID.name: "Nombre en identificación",
  EnumLocal.txtFullAddress.name: "Dirección completa",
  EnumLocal.txtSubmit.name: "Entregar",
  EnumLocal.txtAvailableCoinBalance.name: "Saldo de monedas disponible",
  EnumLocal.txtRechargeCoin.name: "Recargar moneda",
  EnumLocal.txtCoinHistory.name: "Historia de la moneda",
  EnumLocal.txtWithdraw.name: "Retirar",
  EnumLocal.txtAvailableCoin.name: "Moneda disponible",
  EnumLocal.txtEnterCoin.name: "Introduzca moneda",
  EnumLocal.txtEnterWithdrawCoin.name: "Introduzca retirar moneda...",
  EnumLocal.txtMinimumWithdraw.name: "*Retiro mínimo",
  EnumLocal.txtSelectPaymentGateway.name: "Seleccione Pasarela de Pago",
  EnumLocal.txtSelectPaymentMethod.name: "Seleccione método de pago",
  EnumLocal.txtPurchasePremiumPlanAndGetAllAccess.name: "Compre un plan premium y obtenga acceso total",
  EnumLocal.txtRechargePageSubTitle.name: "Actualice al Plan Premium para obtener acceso ilimitado a herramientas avanzadas y recursos exclusivos. Eleva tu experiencia ahora.",
  EnumLocal.txtConfirmWithdrawDialogText.name: "Revise atentamente los detalles del retiro para garantizar su exactitud. Vuelva a verificar la información de la cuenta de destino y el monto del retiro para evitar errores.",
  EnumLocal.txtPurchasePlan.name: "Plan de compra",
  EnumLocal.txtPayNow.name: "Pagar ahora",
  EnumLocal.txtMostPopularPlan.name: "Plan más popular",
  EnumLocal.txtComplaintOrSuggestion.name: "Queja o sugerencia",
  EnumLocal.txtTypingSomethings.name: "Escribiendo algo...",
  EnumLocal.txtContact.name: "Contacto",
  EnumLocal.txtAttachYourImageOrScreenshot.name: "Adjunte su imagen o captura de pantalla",
  EnumLocal.txtBrowse.name: "Navegar",
  EnumLocal.txtNone.name: "Ninguno",
  EnumLocal.txtChooseImage.name: "Elige Imagen",
  EnumLocal.txtGallery.name: "Galería",
  EnumLocal.txtTakePhoto.name: "Tomar foto",
  EnumLocal.txtChooseVideo.name: "Elige vídeo",
  EnumLocal.txtCreateReels.name: "Crear carretes",
  EnumLocal.txtNoDataFound.name: "Datos no encontrados !!",
  EnumLocal.txtTypeSomething.name: "Escribe algo...",
  EnumLocal.txtSomeThingWentWrong.name: "Algo salió mal !!",
  EnumLocal.txtConnectionLost.name: "Conexión perdida !!",
  EnumLocal.txtNoInternetConnection.name: "Sin conexión a Internet !!",
  EnumLocal.txtLoginSuccess.name: "Acceso exitoso",
  EnumLocal.txtProfileUpdateSuccessfully.name: "Actualización de perfil exitosa",
  EnumLocal.txtReelsUploadSuccessfully.name: "Los carretes se cargan exitosamente",
  EnumLocal.txtYouCantFollowYourOwnAccount.name: "No puedes seguir tu propia cuenta.",
  EnumLocal.txtReportSending.name: "Envío de informe...",
  EnumLocal.txtReportSendSuccess.name: "Informe de envío exitoso",
  EnumLocal.txtYouCanSelectMaximumFiveImages.name: "Puedes seleccionar máximo 5 imágenes!!",
  EnumLocal.txtPostUploading.name: "Publicación subiendo...",
  EnumLocal.txtPostUploadSuccessfully.name: "Publicar carga exitosamente",
  EnumLocal.txtPleaseSelectProfileImage.name: "¡¡Por favor selecciona la imagen de perfil!!",
  EnumLocal.txtPleaseEnterFullName.name: "¡Por favor ingrese el nombre completo!",
  EnumLocal.txtPleaseEnterUserName.name: "Por favor ingrese el nombre de usuario!!",
  EnumLocal.txtPleaseEnterBioDetails.name: "¡Por favor ingresa los detalles de la biografía!",
  EnumLocal.txtPleaseAllowPermission.name: "¡Por favor permita el permiso!",
  EnumLocal.txtPleaseWaitSomeTime.name: "Por favor espera algún día...",
  EnumLocal.txtPleaseEnterCaption.name: "¡Por favor ingresa el título!",
  EnumLocal.txtPleaseSelectHashTag.name: "¡Seleccione la etiqueta hash!",
  EnumLocal.txtPleaseSelectPost.name: "Por favor selecciona publicación!!",
  EnumLocal.txtPleaseEnterWithdrawCoin.name: "¡Por favor ingrese retirar moneda!",
  EnumLocal.txtPleaseSelectWithdrawMethod.name: "¡Seleccione el método de retiro!",
  EnumLocal.txtPleaseEnterAllPaymentDetails.name: "¡Por favor ingrese todos los detalles de pago!",
  EnumLocal.txtCoinNotAvailableForSendGiftText.name: "¡¡No tienes fondos suficientes para enviar el regalo!!",
  EnumLocal.txtWithdrawalRequestedCoinMustBeGreaterThanSpecifiedByTheAdmin.name: "¡La moneda solicitada de retiro debe ser mayor que la especificada por el administrador!",
  EnumLocal.txtDownloadSuccess.name: "Descargar Éxito",
  EnumLocal.txtPleaseEnterYourComplain.name: "¡Por favor ingrese su queja!",
  EnumLocal.txtPleaseEnterYourContactNumber.name: "Por favor ingresa tu número de contacto!!",
  EnumLocal.txtPleaseUploadScreenShort.name: "¡¡Por favor sube la pantalla corta!!",
  EnumLocal.txtYourComplainSendSuccessfully.name: "Tu queja se envió exitosamente",
  EnumLocal.txtCoinRechargeSuccess.name: "Éxito en la recarga de monedas",
  EnumLocal.txtPleaseUploadProfileImage.name: "¡¡Por favor sube la imagen de perfil!!",
  EnumLocal.txtPleaseUploadDocumentImage.name: "¡¡Por favor sube la imagen del documento!!",
  EnumLocal.txtPleaseEnterYourIdOnNumber.name: "¡Por favor ingrese su identificación en el número!",
  EnumLocal.txtPleaseEnterYourIdOnName.name: "¡Por favor ingrese su identificación en el nombre!",
  EnumLocal.txtPleaseEnterYourIdOnAddress.name: "¡Por favor ingrese su identificación en la dirección!",
  EnumLocal.txtVerificationRequestSendSuccessfully.name: "La solicitud de verificación se envió correctamente",
  EnumLocal.txtLongPressToEnableAudioRecording.name: "Mantenga presionado para habilitar la grabación de audio",
  EnumLocal.txtAudioRecording.name: "Grabación de audio...",
  EnumLocal.txtOptionalInBrackets.name: "",
  EnumLocal.txtTheUserDoesNotHaveSufficientFundsToMakeTheWithdrawal.name: "",
  EnumLocal.txtVideo.name: "(Opcional)",
  EnumLocal.txtPost.name: "El usuario no tiene fondos suficientes para realizar el retiro.",
  EnumLocal.txtHastTag.name: "Video",
  EnumLocal.txtCaption.name: "Correo",
  EnumLocal.txtReceivedCoin.name: "HastTag",
  EnumLocal.txtSendCoin.name: "Subtítulo",
  EnumLocal.txtAddWallet.name: "Moneda recibida",
  EnumLocal.txtWithdrawal.name: "Enviar moneda",
  EnumLocal.txtPendingWithdrawal.name: "Agregar billetera",
  EnumLocal.txtCancelWithdrawal.name: "Retiro",
  EnumLocal.txtTypeYourHashtag.name: "Escribe tu hashtag...",
  EnumLocal.txtYouCantSendGiftOwnVideo.name: "No puedes enviar un regalo a tu propio vídeo.",
  EnumLocal.txtWelcomeBonusCoin.name: "Moneda de bonificación de bienvenida",
  EnumLocal.txtSendGiftCoin.name: "Enviar moneda de regalo",
  EnumLocal.txtReceiveGiftCoin.name: "Recibir moneda de regalo",
  EnumLocal.txtYouDonHaveSufficientCoinsToSendTheGift.name: "¡¡No tienes suficientes monedas para enviar el regalo!!",
  EnumLocal.txtEnterYourTextWithHashtag.name: "Introduce tu texto con hashtag...",
  EnumLocal.txtDeleteAll.name: "Eliminar todo",
  EnumLocal.txtRequests.name: "Pedido",
  EnumLocal.txtDecline.name: "Rechazar",
  EnumLocal.txtAccept.name: "Aceptar",
  EnumLocal.txtIgnore.name: "Ignorar",
  EnumLocal.txtAcceptMessageRequestFrom.name: "Aceptar solicitud de mensaje de",
  EnumLocal.txtUploadYourFirstVideo.name: "Aceptar solicitud de mensaje de",
  EnumLocal.txtEnterVerificationCode.name: "Sube tu primer vídeo",
  EnumLocal.txtEnterVerificationCodeAsUnder.name: "Ingrese\nCódigo de verificación",
  EnumLocal.txtSendCodeThisNumber.name: "Ingrese el código de verificación como se indica a continuación",
  EnumLocal.txtResendCode.name: "enviar codigo a este numero",
  EnumLocal.EnterMobileNumber.name: "Reenviar código",
  EnumLocal.txtEnterYourMobileNumberHereAndContinue.name: "Ingrese\nNúmero de móvil",
  EnumLocal.txtVerify.name: "Introduce aquí tu número de móvil y continúa...",
  EnumLocal.txtMobileNumHintTest.name: "Verificar",
  EnumLocal.txtPleaseEnterCorrectMobileNumber.name: "Introduce el número de móvil",
  EnumLocal.txtPleaseEnterMobileNumber.name: "Por favor introduce el número de móvil correcto",
  EnumLocal.txtVerificationTimeout.name: "Por favor ingrese el número de móvil",
  EnumLocal.txtVerificationCodeSend.name: "Tiempo de espera de verificación",
  EnumLocal.txtPleaseEnterVerificationCode.name: "Envío de código de verificación.",
  EnumLocal.txtMoreOption.name: "Por favor ingresa el código de verificación",
  EnumLocal.txtDeletePost.name: "Más opciones",
  EnumLocal.txtDeleteVideo.name: "Eliminar publicación",
  EnumLocal.txtDeletePostVideoContent.name: "Eliminar vídeo",
  EnumLocal.txtMobile.name: "Móvil",
  EnumLocal.txtGoogle.name: "Google",
  EnumLocal.txtOr.name: "o",
  EnumLocal.txtUseAudio.name: "usar audio",
  EnumLocal.txtAudio.name: "Audio",
  EnumLocal.txtEditPost.name: "Editar publicación",
  EnumLocal.txtEditReels.name: "Editar carretes",
  EnumLocal.txtEdit.name: "Editar",
};
