import 'dart:convert';
import 'dart:developer';
import 'package:http/http.dart' as http;
import 'package:shortie/pages/reels_page/model/fetch_reels_model.dart';
import 'package:shortie/utils/api.dart';
import 'package:shortie/utils/utils.dart';

class FetchVideoByIdApi {
  static Future<Data?> callApi({
    required String loginUserId,
    required String videoId,
  }) async {
    Utils.showLog("Fetch Video By ID API Call");

    final uri = Uri.parse("${Api.fetchReels}?userId=$loginUserId&videoId=$videoId");

    try {
      final response = await http.get(uri, headers: {"key": Api.secretKey});

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        
        Utils.showLog("Fetch Video By ID API Response => ${response.body}");

        if (jsonResponse['status'] == true && jsonResponse['data'] != null) {
          // Assuming the API returns a single video object
          return Data.fromJson(jsonResponse['data']);
        } else {
          Utils.showLog("Fetch Video By ID API Error => ${jsonResponse['message']}");
          return null;
        }
      } else {
        Utils.showLog("Fetch Video By ID API Error => ${response.statusCode}");
        return null;
      }
    } catch (e) {
      Utils.showLog("Fetch Video By ID API Exception => $e");
      return null;
    }
  }
}
