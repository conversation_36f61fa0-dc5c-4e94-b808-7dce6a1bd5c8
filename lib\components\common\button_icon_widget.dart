import 'package:flutter/material.dart';
import '../../internal/internal_defines.dart';

class ButtonIconWidget extends StatelessWidget {
  final ButtonIcon? buttonIcon;
  final double size;
  final bool isSelected;

  const ButtonIconWidget({
    Key? key,
    this.buttonIcon,
    required this.size,
    this.isSelected = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (buttonIcon == null) {
      return SizedBox(width: size, height: size);
    }

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: buttonIcon!.backgroundColor ?? Colors.transparent,
        borderRadius: BorderRadius.circular(buttonIcon!.borderRadius ?? size / 2),
        border: buttonIcon!.borderColor != null
            ? Border.all(
                color: buttonIcon!.borderColor!,
                width: buttonIcon!.borderWidth ?? 1.0,
              )
            : null,
      ),
      child: Center(
        child: buttonIcon!.icon ?? Builder(builder: (context) {
          // Try to use selected icon URL if available and selected
          if (isSelected && buttonIcon!.selectedIconUrl != null) {
            return Image.asset(
              buttonIcon!.selectedIconUrl!,
              errorBuilder: (context, error, stackTrace) {
                // Fall back to icon if asset fails to load
                return const Icon(
                  Icons.image,
                  color: Colors.white,
                  size: 32,
                );
              },
            );
          }
          
          // Try to use normal icon URL
          if (buttonIcon!.normalIconUrl != null) {
            return Image.asset(
              buttonIcon!.normalIconUrl!,
              errorBuilder: (context, error, stackTrace) {
                // Fall back to icon if asset fails to load
                return const Icon(
                  Icons.image,
                  color: Colors.white,
                  size: 32,
                );
              },
            );
          }
          
          // Ultimate fallback
          return const Icon(
            Icons.image_not_supported,
            color: Colors.white,
            size: 32,
          );
        }),
      ),
    );
  }
}
