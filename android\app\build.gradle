plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}


def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace "com.example.shortie"
    compileSdkVersion 34
    ndkVersion flutter.ndkVersion // Revert to Flutter's managed NDK version

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }
    
    buildFeatures {
        buildConfig = true
    }
    
    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
            excludes += 'META-INF/DEPENDENCIES'
            excludes += 'META-INF/LICENSE'
            excludes += 'META-INF/LICENSE.txt'
            excludes += 'META-INF/license.txt'
            excludes += 'META-INF/NOTICE'
            excludes += 'META-INF/NOTICE.txt'
            excludes += 'META-INF/notice.txt'
            excludes += 'META-INF/ASL2.0'
            excludes += 'META-INF/*.kotlin_module'
        }
    }

    sourceSets {
        main {
            java.srcDirs += 'src/main/kotlin'
            res.srcDirs += new File(projectDir.parentFile.parentFile, 'build/app/RES/processDebugGoogleServices')
        }
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.alviongs.ratulive"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion 24
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
        versionCode 3
        versionName "1.0.3"
//        minifyEnabled true
//        // Enables resource shrinking, which is performed by the
//        // Android Gradle plugin.
//        shrinkResources true
    }

    // Temporarily commented out for debug build
    // signingConfigs {
    //     release {
    //         keyAlias keystoreProperties['keyAlias']
    //         keyPassword keystoreProperties['keyPassword']
    //         storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
    //         storePassword keystoreProperties['storePassword']
    //     }
    // }


    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
//            signingConfig signingConfigs.release
            signingConfig signingConfigs.debug
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation 'androidx.cardview:cardview:1.0.0'
    def exoplayer_version = "2.18.7"
    implementation "com.google.android.exoplayer:exoplayer-core:$exoplayer_version"
    implementation "com.google.android.exoplayer:exoplayer-dash:$exoplayer_version"
    implementation "com.google.android.exoplayer:exoplayer-hls:$exoplayer_version"
    implementation "com.google.android.exoplayer:exoplayer-smoothstreaming:$exoplayer_version"
    implementation 'com.google.android.exoplayer:exoplayer:2.19.1'
    implementation 'com.android.billingclient:billing:5.0.0'


}

// Ensure Firebase resources are generated before they are merged or packaged
project.afterEvaluate {
    tasks.named("mergeDebugResources").configure {
        dependsOn(tasks.named("processDebugGoogleServices"))
    }
    tasks.named("packageDebugResources").configure {
        dependsOn(tasks.named("processDebugGoogleServices"))
    }

    // It's good practice to do this for release builds too, if they exist and use Firebase
    tasks.findByName("mergeReleaseResources")?.configure {
        dependsOn(tasks.findByName("processReleaseGoogleServices"))
    }
    tasks.findByName("packageReleaseResources")?.configure {
        dependsOn(tasks.findByName("processReleaseGoogleServices"))
    }
}

apply plugin: 'com.google.gms.google-services'


