PODS:
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - audioplayers_darwin (0.0.1):
    - Flutter
  - BranchSDK (3.3.0)
  - camera_avfoundation (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
  - deepar_flutter (0.0.5):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - ffmpeg_kit_flutter (6.0.3):
    - ffmpeg_kit_flutter/ffmpeg_kit_ios_local (= 6.0.3)
    - Flutter
  - ffmpeg_kit_flutter/ffmpeg_kit_ios_local (6.0.3):
    - Flutter
  - Firebase/Auth (11.8.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.8.0)
  - Firebase/CoreOnly (11.8.0):
    - FirebaseCore (~> 11.8.0)
  - Firebase/Crashlytics (11.8.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.8.0)
  - Firebase/Messaging (11.8.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.8.0)
  - firebase_auth (5.5.1):
    - Firebase/Auth (= 11.8.0)
    - firebase_core
    - Flutter
  - firebase_core (3.12.1):
    - Firebase/CoreOnly (= 11.8.0)
    - Flutter
  - firebase_crashlytics (4.3.4):
    - Firebase/Crashlytics (= 11.8.0)
    - firebase_core
    - Flutter
  - firebase_messaging (15.2.4):
    - Firebase/Messaging (= 11.8.0)
    - firebase_core
    - Flutter
  - FirebaseAppCheckInterop (11.12.0)
  - FirebaseAuth (11.8.1):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.8.0)
    - FirebaseCoreExtension (~> 11.8.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
    - RecaptchaInterop (~> 100.0)
  - FirebaseAuthInterop (11.12.0)
  - FirebaseCore (11.8.1):
    - FirebaseCoreInternal (~> 11.8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.8.0):
    - FirebaseCore (~> 11.8.0)
  - FirebaseCoreInternal (11.8.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseCrashlytics (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseInstallations (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - FirebaseRemoteConfigInterop (11.12.0)
  - FirebaseSessions (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - FirebaseCoreExtension (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - Flutter (1.0.0)
  - flutter_branch_sdk (7.1.0):
    - BranchSDK (~> 3.3.0)
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
  - gallery_saver_plus (0.0.1):
    - Flutter
  - Google-Mobile-Ads-SDK (11.10.0):
    - GoogleUserMessagingPlatform (>= 1.1)
  - google_mobile_ads (5.2.0):
    - Flutter
    - Google-Mobile-Ads-SDK (~> 11.10.0)
    - webview_flutter_wkwebview
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 7.1)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMLKit/BarcodeScanning (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitBarcodeScanning (~> 6.0.0)
  - GoogleMLKit/MLKitCore (7.0.0):
    - MLKitCommon (~> 12.0.0)
  - GoogleSignIn (7.1.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleToolboxForMac/Defines (4.2.1)
  - GoogleToolboxForMac/Logger (4.2.1):
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - "GoogleToolboxForMac/NSData+zlib (4.2.1)":
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - GoogleUserMessagingPlatform (3.0.0)
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - image_picker_ios (0.0.1):
    - Flutter
  - in_app_purchase_storekit (0.0.1):
    - Flutter
    - FlutterMacOS
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - MLImage (1.0.0-beta6)
  - MLKitBarcodeScanning (6.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitVision (~> 8.0)
  - MLKitCommon (12.0.0):
    - GoogleDataTransport (~> 10.0)
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GoogleUtilities/Logger (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
  - MLKitVision (8.0.0):
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
    - MLImage (= 1.0.0-beta6)
    - MLKitCommon (~> 12.0)
  - mobile_scanner (6.0.2):
    - Flutter
    - GoogleMLKit/BarcodeScanning (~> 7.0.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - native_device_orientation (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.1.1):
    - Flutter
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - razorpay-pod (1.4.0)
  - razorpay_flutter (1.1.10):
    - Flutter
    - razorpay-pod
  - RecaptchaInterop (100.0.0)
  - record_darwin (1.0.0):
    - Flutter
  - share_plus (0.0.1):
    - Flutter
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - Stripe (23.27.6):
    - StripeApplePay (= 23.27.6)
    - StripeCore (= 23.27.6)
    - StripePayments (= 23.27.6)
    - StripePaymentsUI (= 23.27.6)
    - StripeUICore (= 23.27.6)
  - stripe_ios (0.0.1):
    - Flutter
    - Stripe (~> 23.27.0)
    - StripeApplePay (~> 23.27.0)
    - StripeFinancialConnections (~> 23.27.0)
    - StripePayments (~> 23.27.0)
    - StripePaymentSheet (~> 23.27.0)
    - StripePaymentsUI (~> 23.27.0)
  - StripeApplePay (23.27.6):
    - StripeCore (= 23.27.6)
  - StripeCore (23.27.6)
  - StripeFinancialConnections (23.27.6):
    - StripeCore (= 23.27.6)
    - StripeUICore (= 23.27.6)
  - StripePayments (23.27.6):
    - StripeCore (= 23.27.6)
    - StripePayments/Stripe3DS2 (= 23.27.6)
  - StripePayments/Stripe3DS2 (23.27.6):
    - StripeCore (= 23.27.6)
  - StripePaymentSheet (23.27.6):
    - StripeApplePay (= 23.27.6)
    - StripeCore (= 23.27.6)
    - StripePayments (= 23.27.6)
    - StripePaymentsUI (= 23.27.6)
  - StripePaymentsUI (23.27.6):
    - StripeCore (= 23.27.6)
    - StripePayments (= 23.27.6)
    - StripeUICore (= 23.27.6)
  - StripeUICore (23.27.6):
    - StripeCore (= 23.27.6)
  - url_launcher_ios (0.0.1):
    - Flutter
  - vibration (1.7.5):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - video_thumbnail (0.0.1):
    - Flutter
    - libwebp
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
  - zego_express_engine (3.19.1):
    - Flutter

DEPENDENCIES:
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - deepar_flutter (from `.symlinks/plugins/deepar_flutter/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - ffmpeg_kit_flutter (from `.symlinks/plugins/ffmpeg_kit_flutter/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_branch_sdk (from `.symlinks/plugins/flutter_branch_sdk/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - gallery_saver_plus (from `.symlinks/plugins/gallery_saver_plus/ios`)
  - google_mobile_ads (from `.symlinks/plugins/google_mobile_ads/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - in_app_purchase_storekit (from `.symlinks/plugins/in_app_purchase_storekit/darwin`)
  - mobile_scanner (from `.symlinks/plugins/mobile_scanner/ios`)
  - native_device_orientation (from `.symlinks/plugins/native_device_orientation/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - razorpay_flutter (from `.symlinks/plugins/razorpay_flutter/ios`)
  - record_darwin (from `.symlinks/plugins/record_darwin/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/darwin`)
  - stripe_ios (from `.symlinks/plugins/stripe_ios/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - vibration (from `.symlinks/plugins/vibration/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - video_thumbnail (from `.symlinks/plugins/video_thumbnail/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)
  - zego_express_engine (from `.symlinks/plugins/zego_express_engine/ios`)

SPEC REPOS:
  trunk:
    - AppAuth
    - BranchSDK
    - Firebase
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - Google-Mobile-Ads-SDK
    - GoogleDataTransport
    - GoogleMLKit
    - GoogleSignIn
    - GoogleToolboxForMac
    - GoogleUserMessagingPlatform
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - libwebp
    - MLImage
    - MLKitBarcodeScanning
    - MLKitCommon
    - MLKitVision
    - nanopb
    - PromisesObjC
    - PromisesSwift
    - razorpay-pod
    - RecaptchaInterop
    - Stripe
    - StripeApplePay
    - StripeCore
    - StripeFinancialConnections
    - StripePayments
    - StripePaymentSheet
    - StripePaymentsUI
    - StripeUICore

EXTERNAL SOURCES:
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  deepar_flutter:
    :path: ".symlinks/plugins/deepar_flutter/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  ffmpeg_kit_flutter:
    :path: ".symlinks/plugins/ffmpeg_kit_flutter/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_branch_sdk:
    :path: ".symlinks/plugins/flutter_branch_sdk/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  gallery_saver_plus:
    :path: ".symlinks/plugins/gallery_saver_plus/ios"
  google_mobile_ads:
    :path: ".symlinks/plugins/google_mobile_ads/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  in_app_purchase_storekit:
    :path: ".symlinks/plugins/in_app_purchase_storekit/darwin"
  mobile_scanner:
    :path: ".symlinks/plugins/mobile_scanner/ios"
  native_device_orientation:
    :path: ".symlinks/plugins/native_device_orientation/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  razorpay_flutter:
    :path: ".symlinks/plugins/razorpay_flutter/ios"
  record_darwin:
    :path: ".symlinks/plugins/record_darwin/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/darwin"
  stripe_ios:
    :path: ".symlinks/plugins/stripe_ios/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  vibration:
    :path: ".symlinks/plugins/vibration/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  video_thumbnail:
    :path: ".symlinks/plugins/video_thumbnail/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"
  zego_express_engine:
    :path: ".symlinks/plugins/zego_express_engine/ios"

SPEC CHECKSUMS:
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  audioplayers_darwin: 877d9a4d06331c5c374595e46e16453ac7eafa40
  BranchSDK: 262dbab56b767807f49f45191c8499cd9c4b4d76
  camera_avfoundation: dd002b0330f4981e1bbcb46ae9b62829237459a4
  connectivity_plus: 2a701ffec2c0ae28a48cf7540e279787e77c447d
  deepar_flutter: e89060abce0c460f36a0bf73802f8a5443f1fdb5
  device_info_plus: 97af1d7e84681a90d0693e63169a5d50e0839a0d
  ffmpeg_kit_flutter: 4c1741908fce335f6f3e021d6829686c0b1109c7
  Firebase: d80354ed7f6df5f9aca55e9eb47cc4b634735eaf
  firebase_auth: 3d848b9b866b201e5c8e0c06d8b2cec272fd8825
  firebase_core: ac395f994af4e28f6a38b59e05a88ca57abeb874
  firebase_crashlytics: 0f3a46f30fce2159f715ac33eef98a2aa5d598f1
  firebase_messaging: 7e223f4ee7ca053bf4ce43748e84a6d774ec9728
  FirebaseAppCheckInterop: 73b173e5ec45192e2d522ad43f526a82ad10b852
  FirebaseAuth: ad59a1a7b161e75f74c39f70179d2482d40e2737
  FirebaseAuthInterop: b583210c039a60ed3f1e48865e1f3da44a796595
  FirebaseCore: 99fe0c4b44a39f37d99e6404e02009d2db5d718d
  FirebaseCoreExtension: 3d3f2017a00d06e09ab4ebe065391b0bb642565e
  FirebaseCoreInternal: df24ce5af28864660ecbd13596fc8dd3a8c34629
  FirebaseCrashlytics: a1102c035f18d5dd94a5969ee439c526d0c9e313
  FirebaseInstallations: 6c963bd2a86aca0481eef4f48f5a4df783ae5917
  FirebaseMessaging: 487b634ccdf6f7b7ff180fdcb2a9935490f764e8
  FirebaseRemoteConfigInterop: 82b81fd06ee550cbeff40004e2c106daedf73e38
  FirebaseSessions: c4d40a97f88f9eaff2834d61b4fea0a522d62123
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_branch_sdk: 1ca1798cfb9ff9acf11028dee0592e3fa0234649
  flutter_local_notifications: 4cde75091f6327eb8517fa068a0a5950212d2086
  fluttertoast: 21eecd6935e7064cc1fcb733a4c5a428f3f24f0f
  gallery_saver_plus: c4b59c605df1049a02d036ef81d82972b6dfd606
  Google-Mobile-Ads-SDK: 13e6e98edfd78ad8d8a791edb927658cc260a56f
  google_mobile_ads: 2a538d8e42b1813809782792e48f8cf4374c2180
  google_sign_in_ios: 4111e87aa5e24a4404f00ea13479f35e571969cc
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMLKit: eff9e23ec1d90ea4157a1ee2e32a4f610c5b3318
  GoogleSignIn: d4281ab6cf21542b1cfaff85c191f230b399d2db
  GoogleToolboxForMac: d1a2cbf009c453f4d6ded37c105e2f67a32206d8
  GoogleUserMessagingPlatform: f8d0cdad3ca835406755d0a69aa634f00e76d576
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  in_app_purchase_storekit: a1ce04056e23eecc666b086040239da7619cd783
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  MLImage: 0ad1c5f50edd027672d8b26b0fee78a8b4a0fc56
  MLKitBarcodeScanning: 0a3064da0a7f49ac24ceb3cb46a5bc67496facd2
  MLKitCommon: 07c2c33ae5640e5380beaaa6e4b9c249a205542d
  MLKitVision: 45e79d68845a2de77e2dd4d7f07947f0ed157b0e
  mobile_scanner: fd0054c52ede661e80bf5a4dea477a2467356bee
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  native_device_orientation: 348b10c346a60ebbc62fb235a4fdb5d1b61a8f55
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: e76247795d700c14ea09e3a2d8855d41ee80a2e6
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  razorpay-pod: cb5b5439697bdda9c57d68a92b3e5b0ce1d2f2f3
  razorpay_flutter: 84b3bfd206ae9c9c2a9ba585524a1b3d8102b6c1
  RecaptchaInterop: 7d1a4a01a6b2cb1610a47ef3f85f0c411434cb21
  record_darwin: 3b1a8e7d5c0cbf45ad6165b4d83a6ca643d929c3
  share_plus: 8b6f8b3447e494cca5317c8c3073de39b3600d1f
  sqflite: 673a0e54cc04b7d6dba8d24fb8095b31c3a99eec
  Stripe: 9fec845645e39f371e6898926d096fd9c2feb5a5
  stripe_ios: 03c617acee72e48a2d055d096a4b0ed2afebb256
  StripeApplePay: 5f017e8dfe259fafbab70137776189deef754bb2
  StripeCore: 01ec57f0dddfe742054dc6a322f811426c25313d
  StripeFinancialConnections: 56698cb6274bf89fb8c76b934f6156f368e97765
  StripePayments: 6adf11faf1b7038e77aa97019410305c6adca79d
  StripePaymentSheet: 3eaf870c4388e44b0cc37e4c69d00b6957fd8bd7
  StripePaymentsUI: 59ccddeacad592b09fa67e8d641340820ddb4751
  StripeUICore: 879bbf5889265db13f52fac8aad7a176ba62481f
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  vibration: 7d883d141656a1c1a6d8d238616b2042a51a1241
  video_player_avfoundation: 7c6c11d8470e1675df7397027218274b6d2360b3
  video_thumbnail: c4e2a3c539e247d4de13cd545344fd2d26ffafd1
  wakelock_plus: 373cfe59b235a6dd5837d0fb88791d2f13a90d56
  webview_flutter_wkwebview: 2a23822e9039b7b1bc52e5add778e5d89ad488d1
  zego_express_engine: 828102bd0bc176d91663390152bcd78013f28d48

PODFILE CHECKSUM: 4c438addb11b6da45ed7ae408823d68256222460

COCOAPODS: 1.16.2
