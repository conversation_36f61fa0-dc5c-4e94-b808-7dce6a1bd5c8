import 'package:flutter/foundation.dart';

/// Simple role enumeration for live streaming
enum ZegoLiveStreamingRole {
  audience,
  host,
}

/// A simple manager to track the current user role in live streaming.
/// This replaces the complex ZegoLiveStreamingManager with just the essential
/// role tracking functionality.
class ZegoRoleManager {
  ZegoRoleManager._internal();
  factory ZegoRoleManager() => _instance;
  static final ZegoRoleManager _instance = ZegoRoleManager._internal();

  /// Current user role in the live stream
  final ValueNotifier<ZegoLiveStreamingRole> currentUserRoleNoti = ValueNotifier(ZegoLiveStreamingRole.audience);
  
  /// Set user role
  void setRole(ZegoLiveStreamingRole role) {
    currentUserRoleNoti.value = role;
  }
}
