<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Ratu</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>shortie</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.shortie.live</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.62445718384-8djj0eqqvfbhf01bjj57c0to9usrkv06</string>
				<string>Enter your custom scheme here</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>GADApplicationIdentifier</key>
	<string>ca-app-pub-3940256099942544~3347511713</string>
	<key>GADNativeAdValidatorEnabled</key>
    <false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSCameraUsageDescription</key>
	<string>Take a photo for display</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Take a video for display</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Read your photos for display</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>My description about why I need this capability</string>
	<key>PHPhotoLibraryPreventAutomaticLimitedAccessAlert</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>branch_key</key>
	<dict>
		<key>live</key>
		<string>Enter your live secret </string>
		<key>test</key>
		<string>Enter your test secret</string>
	</dict>
	<key>branch_universal_link_domains</key>
	<array>
		<string>Enter_your_test_app_link</string>
		<string>Enter_your_live_app_link</string>
		<string>Enter_your_staging_app_link</string>
	</array>
</dict>
</plist>
