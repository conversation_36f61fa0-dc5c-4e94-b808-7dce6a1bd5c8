import 'package:flutter/material.dart';
import 'package:zego_express_engine/zego_express_engine.dart';

import '../../zego_sdk_manager.dart';

/// Toggle microphone on/off
class ZegoToggleMicrophoneButton extends StatelessWidget {
  const ZegoToggleMicrophoneButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final currentUser = ZEGOSDKManager().currentUser;
    if (currentUser == null) return const SizedBox.shrink();
    
    return ValueListenableBuilder<bool>(
      valueListenable: currentUser.isMicOnNotifier,
      builder: (context, isMicOn, _) {
        return GestureDetector(
          onTap: () {
            final newValue = !isMicOn;
            currentUser.isMicOnNotifier.value = newValue;
            ZegoExpressEngine.instance.muteMicrophone(!newValue);
          },
          child: Container(
            width: 96,
            height: 96,
            decoration: BoxDecoration(
              color: isMicOn 
                  ? const Color.fromARGB(255, 51, 52, 56).withOpacity(0.6) 
                  : Colors.grey,
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white.withOpacity(0.3), width: 1),
            ),
            child: Center(
              child: Icon(
                isMicOn ? Icons.mic : Icons.mic_off,
                color: Colors.white,
                size: 32,
              ),
            ),
          ),
        );
      },
    );
  }
}
