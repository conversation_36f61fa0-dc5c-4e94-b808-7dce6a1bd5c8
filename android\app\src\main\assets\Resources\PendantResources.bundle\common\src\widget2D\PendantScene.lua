---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by sterling.
--- DateTime: 3/23/21 10:26 AM
---


if not json then
    require "cocos.cocos2d.json"
end

require "cocos.init"
require "util"
require "widget2D.constant"

local PendantScene = class("PendantScene")

--local s = nil
function PendantScene:ctor(kiwiEngine)

    self.kiwiEngine = kiwiEngine
    self.width = kiwiEngine.width
    self.height = kiwiEngine.height

    self.minEngineVersion = nil
    self.version = nil
    self.debug = nil
    self.type = nil

    self.mediaSprite = nil
    self.mediaSegment = nil
    self.mediaFaceSegment = nil

    self.faceInfos = {}
    self.faceNum = 0

    self.bgs = {}
    self.fgs = {}

    self.hasGroudAnim = false
    self.animations = {}
    self.animationParams = {}
    self.faceFilms = {} --脸膜

    self.musics = {}
    self.state = nil

    self.curBg = nil
    self.curFg = nil

    self.ball = nil

    -- 脸挂件资源
    self.faceRes = {} --face资源保存
    self.faceOrder = {1,1} --两个face使用资源的索引

    ---- 每人一个挂件组件
    self.pendents = {}-- face挂件集

    self.supportETC1 = true
    self.aiSegment = false
    
    self.scene = cc.Scene:create()
    --s = self

    local visibleSize = cc.Director:getInstance():getVisibleSize()
    local cameraDistance = 1000
    local cameraWidth = 750
    local cameraHeight = -1000
    local cameraDepth = 1500

    self.camera = cc.Camera:createOrthographic(visibleSize.width, visibleSize.height, 1, cameraDepth*2)
    self.camera:setPosition3D(cc.vec3(visibleSize.width ,visibleSize.height , cameraDepth))
    --     self.camera:setCameraFlag(1)
    self.camera:setDepth(-1)
    self.scene:addChild(self.camera)

    self:parsePendantJsonFile(constant.json.file)
    self:setupKiwiEngine()
    self.scene:sortAllChildren()
    self:run()
end

function PendantScene:run()
    if cc.Director:getInstance():getRunningScene() then
        print("run Scene replace")
        cc.Director:getInstance():replaceScene(self.scene)
    else
        print("run Scene run")
        cc.Director:getInstance():runWithScene(self.scene)
    end
end

function PendantScene:setupMedia()
    if self.kiwiEngine ~= nil and self.kiwiEngine.media ~= nil and self.kiwiEngine.media.available == true then
        self.mediaSprite = self.kiwiEngine.media

        if self.curFg ~= nil then
            self:createFilterMedia(self.curFg.elements, "media", self.mediaSprite, constant.order.foreground)
        end
        if self.curBg ~= nil then
            self:createFilterMedia(self.curBg.elements, "media", self.mediaSprite, constant.order.background)
        end
    end
end

function PendantScene:setupSegment()
    if self.kiwiEngine ~= nil and self.kiwiEngine.segment ~= nil and  self.kiwiEngine.segment.available == true then
        self.mediaSegment = self.kiwiEngine.segment
        if self.curFg ~= nil then
            self:createFilterMedia(self.curFg.elements, "segment", self.mediaSegment, constant.order.foreground)
        end

        if self.curBg ~= nil then
            self:createFilterMedia(self.curBg.elements, "segment", self.mediaSegment , constant.order.background)
        end
    end
end

function PendantScene:setupFaceSegment()
    if self.kiwiEngine ~= nil and self.kiwiEngine.faceSegment ~= nil then
        self.mediaFaceSegment = self.kiwiEngine.faceSegment

        --if self.curFg ~= nil then
        --    self:createFilterMedia(self.curFg.elements, "faceSegment", self.mediaFaceSegment ,constant.order.foreground)
        --end
        --
        --if self.curBg ~= nil then
        --    self:createFilterMedia(self.curBg.elements, "faceSegment", self.mediaFaceSegment, constant.order.background)
        --end
    end
end

function PendantScene:processFaceExit(lastInfos, faceInfos)
    for k,v in pairs(lastInfos) do
        if faceInfos[k] == nil and self.pendents[k] ~= nil then
            self:triggerFaceEvent(constant.event.faceExit, k)
            self.pendents[k]:finish()
            self.pendents[k]:onFinish()
            self.pendents[k] = nil
            print("finish face pendents:" , k)
        end
    end
end

function PendantScene:processFaceEnter(lastInfos, faceInfos)
    for k,v in pairs(faceInfos) do
        if k > 0 and k <= constant.MAX_FACE_INDEX and self.pendents[k] == nil and self.faceRes[self.faceOrder[k]] ~= nil then
            self.pendents[k] = require("widget2D.Pendant2D"):create()
            --人脸的index
            self.pendents[k].index = k
            self.pendents[k].scene = self.scene
            self.pendents[k].supportETC1 = self.supportETC1
            if self.stateJson ~= nil then
                self.pendents[k].state = self.stateJson.default
            end
            --给定资源index
            self.pendents[k].resIndex = self.faceOrder[k]
            self.pendents[k].mediaSprite = self.mediaSprite
            self.pendents[k].mediaSegment = self.mediaSegment
            if self.mediaFaceSegment ~= nil and self.mediaFaceSegment[k] ~= nil then
                self.pendents[k].mediaFaceSegment = self.mediaFaceSegment[k]
            end
            self.pendents[k]:createWidget2Ds(self.faceRes[self.faceOrder[k]].widgetJsonArray)

            --self.pendents[k]:createAnims(self.animationJsonArray)
            self:triggerFaceEvent(constant.event.faceEnter, k)
            self.pendents[k]:onCreate()
            print("create face pendents :" , k)

            --替换脸部
            --if s.faceRes[s.faceOrder[k]].replace_texture ~= nil then
            --    s:replaceFace(s.faceRes[s.faceOrder[k]].replace_texture)
            --end
        end

        if k > 0 and k <= constant.MAX_FACE_INDEX then
            self:detectFaceEvent(lastInfos, k)
            self:trackFace(k)
        end
    end
end

-- 人脸影像与数据
function PendantScene:setupKiwiEngine()
    local s = self
    --media
    self:setupMedia()
    --segment
    self:setupSegment()
    --face segment
    self:setupFaceSegment()

    if self.hasGroudAnim == false then
         s:trigger(constant.event.sceneBegin)
         s:triggerGroundEvent(constant.event.sceneBegin, false)
         s:triggerGroundEvent(constant.event.sceneBegin, true)
    end

    if self.ball ~= nil then
--        self.ball:stopAllActions()
        self.ball:runAction(self.animations["ballAnim"])
    end

    self.kiwiEngine.face.onFaceLandmarks = function(info)
        --printf("west onFaceLandmarks")

        local faceInfos = info.faces
        local faceNum = info.faceNum
        --保存上一次的
        local lastNum = s.faceNum
        local lastInfos = s.faceInfos
        s:processFaceExit(lastInfos, faceInfos)
        s.faceNum = faceNum
        s.faceInfos = faceInfos
        s:processFaceEnter(lastInfos,faceInfos)
    end

end

function PendantScene:calculateInMouse(faceData, eats)
    local upper = faceData[constant.mesh.indexMouseUpper  + 1] -- lua index从1开始
    local lower = faceData[constant.mesh.indexMouseLower  + 1]
    local left = faceData[constant.mesh.indexMouseLeft  + 1]
    local right = faceData[constant.mesh.indexMouseRight  + 1]

    if eats ~= nil then
        for k,v in pairs(eats) do
            if v.node ~= nil then
                local x = v.node:getPositionX()
                local y = v.node:getPositionY()

                if x > left.x and x < right.x and y < upper.y and y > lower.y then
                    print("mouse in", x, y, left.x, right.x, upper.y, lower.y)
                    return true, v.node
                end
            end
        end
    end

    return false, nil
end

function PendantScene:calculateMouseOpen(faceData)
    local upper = faceData.faceMesh[constant.mesh.indexMouseUpper  + 1] -- lua index从1开始
    local lower = faceData.faceMesh[constant.mesh.indexMouseLower  + 1]

    local center = {}
    center.x = (upper.x + lower.x) / 2
    center.y = (upper.y + lower.y) / 2

    -- local dis = math.pow(math.pow(upperLip.x - lowerLip.x, 2) + math.pow(upperLip.y - lowerLip.y, 2), 0.5);
    local dis = util.distance(upper.x, upper.y, lower.x, lower.y) / faceData.height

    if dis >= constant.MOUSE_CLOSE_DISTANCE then
        --print(" MOUSE_CLOSE_DISTANCE  ", dis)
        return true, center
    end
    return false, center
end

function PendantScene:calculateEyeClose(faceData)
    local leftupperEye = faceData.faceMesh[constant.mesh.indexLeftEyeUpper  + 1] -- lua index从1开始
    local leftlowerEye = faceData.faceMesh[constant.mesh.indexLeftEyeLower  + 1]
    local rightupperEye = faceData.faceMesh[ constant.mesh.indexRightEyeUpper + 1] -- lua index从1开始
    local rightlowerEye = faceData.faceMesh[constant.mesh.indexRightEyeLower + 1]

    local leftDis = util.distance(leftupperEye.x, leftupperEye.y, leftlowerEye.x, leftlowerEye.y) / faceData.height
    local rightDis = util.distance(rightupperEye.x, rightupperEye.y, rightlowerEye.x, rightlowerEye.y) / faceData.height

    --print("leftDis, rightDis eys", leftDis, rightDis)
    if leftDis < constant.EYE_CLOSE_DISTANCE or rightDis < constant.EYE_CLOSE_DISTANCE  then
        --print(" eye close ", leftDis, rightDis)
        return true
    end
    return false
end

function PendantScene:faceTrigger(event, faceIndex)
    self:triggerFaceEvent(event, faceIndex)
    self:triggerStateEvent(event, faceIndex)
    self:triggerGroundEvent(event, false)
    self:triggerGroundEvent(event, true)
end

function PendantScene:detectEye(lastInfo , faceInfo, faceIndex)
    local lastEyeClose = false
    local curEyeClose = false
    if lastInfo ~= nil then
        lastEyeClose = self:calculateEyeClose(lastInfo)
    end
    if faceInfo ~= nil then
        curEyeClose = self:calculateEyeClose(faceInfo)
    end

    if curEyeClose == true and lastEyeClose == false then
        --print("curEyeClose ", curEyeClose)
        self:faceTrigger(constant.event.eyeClose, faceIndex)
    elseif curEyeClose == false and lastEyeClose == true then
        --print("curEyeOpen ", lastEyeClose)
        self:faceTrigger(constant.event.eyeOpen, faceIndex)
    end
end

function PendantScene:detectMouse(lastInfo , faceInfo, faceIndex)
    local lastMouseOpen = false
    local lastCenter
    local curMouseOpen = false
    local curCenter

    if lastInfo ~= nil then
        lastMouseOpen, lastCenter = self:calculateMouseOpen(lastInfo)
    end
    if faceInfo ~= nil then
        curMouseOpen, curCenter = self:calculateMouseOpen(faceInfo)
    end

    if curMouseOpen == true and lastMouseOpen == false then
        --print("curMouseOpen ", curMouseOpen, lastMouseOpen)
        self:faceTrigger(constant.event.mouseOpen, faceIndex)
        if self.curFg ~= nil then
            self:eatAnim(curCenter, self.curFg.eats, faceIndex)
        end
    elseif  curMouseOpen == false and lastMouseOpen == true then
        --print("curMouseClose ", curMouseOpen, lastMouseOpen)
        self:faceTrigger(constant.event.mouseClose, faceIndex)
    end
end

function PendantScene:detectHeadNod(curInfo, faceIndex, detectCount, angleLimit)
    if self.HeadNodAngles == nil then
        self.HeadNodAngles = {}
    end
--    print( "x angle " ,  curInfo.xAngle)
    if table.getn(self.HeadNodAngles) == detectCount then
        table.remove(self.HeadNodAngles, 1)
    end
    table.insert(self.HeadNodAngles, curInfo.xAngle)

    if table.getn(self.HeadNodAngles) == detectCount then
--        print( "y angle " ,  curInfo.yAngle)
        local great1 = false
        local great2 = false
        for i = 1, detectCount / 2 do

--            print("node x 1 ", self.HeadNodAngles[i] - self.HeadNodAngles[1] )
            if self.HeadNodAngles[i] - self.HeadNodAngles[1]< angleLimit then
                great1 = true
            end
        end

        for i = detectCount / 2 + 1, detectCount do
--            print("node x 2 ", self.HeadNodAngles[i] - self.HeadNodAngles[1] )
            if self.HeadNodAngles[i] - self.HeadNodAngles[1]  > angleLimit then
                great2 = true
            end
        end

        if great1 == true and great2 == true then
            print("nod head event")
            self.HeadNodAngles = {}
            self:faceTrigger(constant.event.headNod, faceIndex)
        end
    end
end

function PendantScene:detectHeadShake(curInfo, faceIndex, detectCount, angleLimit)

    if self.shakeHeadAngles == nil then
        self.shakeHeadAngles = {}
    end
    --print( "x angle " ,  curInfo.xAngle)
    --local detectCount = 40
    if table.getn(self.shakeHeadAngles) == detectCount then
        table.remove(self.shakeHeadAngles, 1)
    end
    table.insert(self.shakeHeadAngles, curInfo.yAngle)

    if table.getn(self.shakeHeadAngles) == detectCount then
        --print( "y angle " ,  curInfo.yAngle)
        local great1 = false
        local great2 = false
        for i = 1, detectCount / 2 do
            if self.shakeHeadAngles[i] > angleLimit then
                great1 = true
            elseif self.shakeHeadAngles[i] < (0 - angleLimit) then
                great2 = true
            end
        end

        for i = detectCount / 2 + 1, detectCount do
            if self.shakeHeadAngles[i] > angleLimit then
                great1 = true
            elseif self.shakeHeadAngles[i] < (0 - angleLimit) then
                great2 = true
            end
        end

        if great1 == true and great2 == true then
            print("shake event")
            self.shakeHeadAngles = {}
            self:faceTrigger(constant.event.headShake, faceIndex)
        end
    end
end

--人脸事件
function PendantScene:detectFaceEvent(lastInfos, faceIndex)

    if lastInfos ~= nil and lastInfos[faceIndex] ~= nil and self.faceInfos ~= nil and self.faceInfos[faceIndex] ~= nil then
        local lastInfo = lastInfos[faceIndex]
        local curInfo = self.faceInfos[faceIndex]

        self:detectEye(lastInfo, curInfo, faceIndex)
        self:detectMouse(lastInfo, curInfo, faceIndex)
        self:detectHeadShake( curInfo, faceIndex, constant.HEAD_SHAKE_DETECT_COUNT, constant.HEAD_SHAKE_ANGLE_LIMIT)
        self:detectHeadNod(curInfo, faceIndex, constant.HEAD_NOD_DETECT_COUNT , constant.HEAD_NOD_ANGLE_LIMIT)
    end

end

function PendantScene:eatAnim(mouseCenter, eats, faceIndex)
    local s = self
    if eats ~= nil then
        for k,v in pairs(eats) do
            if v.node ~= nil then
                print(" eatAnim open ", mouseCenter.x, mouseCenter.y)
                local move = cc.MoveTo:create(1, cc.p(mouseCenter.x, mouseCenter.y))
                local actions = {}
                actions[#actions + 1] = move
                actions[#actions + 1] = cc.CallFunc:create(function()
                    print(" eatAnim open triggerStateEvent")
                    s:triggerStateEvent(constant.event.mouseEat, faceIndex)
                end)
                local action = cc.Sequence:create(actions)
                v.node:runAction(action)
            end
        end
    end
end

--跟踪人脸
function PendantScene:trackFace(faceIndex)
    --获取对应挂件
    local pendant = self.pendents[faceIndex]
    --获取对应人脸
    local faceData = self.faceInfos[faceIndex].faceMesh
    local faceInfo = self.faceInfos[faceIndex]
    local cosValue = math.cos(faceInfo.zAngle)
    local sinValue = math.sin(faceInfo.zAngle)
    for k,v in pairs(pendant.nodesByFace) do
        local mountPoint = pendant.nodeFaceJson[k].mountPoint
        -- 挂节点有效 > 0
        if mountPoint > 0 then
            local point = faceData[mountPoint + 1]
            --位置
            v:setPosition(point.x + faceInfo.width * pendant.nodeFaceJson[k].positionRate.x * cosValue
                    + faceInfo.height * pendant.nodeFaceJson[k].positionRate.y * sinValue,
                    point.y + faceInfo.height * pendant.nodeFaceJson[k].positionRate.y * cosValue
                            - faceInfo.width * pendant.nodeFaceJson[k].positionRate.x * sinValue)

            --比例， 按头宽等比缩放
            local scaleValue = faceInfo.width * pendant.nodeFaceJson[k].scaleRate.x /  v:getContentSize().width
            if (v:getContentSize().width > 0 and v:getContentSize().height > 0) then
                v:setScale(scaleValue, scaleValue)
            end
            -- 旋转, 根据rotateFaceMask 来控制哪个轴进行旋转
            local xAngle = faceInfo.xAngle / 3.1415926 * 180.0
            local yAngle = faceInfo.yAngle / 3.1415926 * 180.0
            local zAngle = faceInfo.zAngle / 3.1415926 * 180.0
            local rotateMask = pendant.nodeFaceJson[k].rotateFaceMask

            if rotateMask ~= nil then
                if rotateMask["x"] == 0 then
                    xAngle = 0
                end
                if rotateMask["y"] == 0 then
                    yAngle = 0
                end
                if rotateMask["z"] == 0 then
                    zAngle = 0
                end
                --print("rotate mask", rotateMask["x"], rotateMask["y"], rotateMask["z"] )
            end

            v:setRotation3D(cc.vec3(xAngle, yAngle, zAngle))

        end

    end
end

function PendantScene:setFace(pendant, resIndex)
    --print (" setFace", pendant.resIndex, resIndex)
    if  pendant == nil or pendant.resIndex == resIndex then
        print ("no need setFace")
        return
    end

    if  self.faceRes[resIndex] == nil or  self.faceRes[resIndex].widgetJsonArray == nil then
        print ("resIndex is nil")
        return
    end

    local i = pendant.index
    local state = pendant.state

    if pendant ~= nil then
        pendant:finish()
        pendant:onFinish()
        pendant = nil
    end

    self.pendents[i] = require("widget2D.Pendant2D"):create()
    self.pendents[i].index = i
    self.pendents[i].scene = self.scene
    self.pendents[i].state = state
    self.pendents[i].supportETC1 = self.supportETC1

    self.pendents[i]:createWidget2Ds(self.faceRes[resIndex].widgetJsonArray)
    self.pendents[i].resIndex = resIndex
    self.faceOrder[i] = resIndex
    --self.pendents[i]:createAnims(self.animationJsonArray)
    self:triggerFaceEvent(constant.event.faceEnter, i)
    self.pendents[i]:onCreate()
end

-- 触发状态到某脸部
function PendantScene:triggerStateEvent(event, faceIndex)
    for k1,p in pairs(self.pendents) do
        if k1 == faceIndex and p.state ~= nil and self.stateJson ~= nil then
            for ks,s in pairs(self.stateJson.switch) do
                local bSwitch = false
                for k2,e in pairs(s.events) do
                    if s.from == p.state and e == event then
                        p.state = s.to
                        bSwitch = true
                        for k3, a in pairs(s.actions) do
                            if a.type == constant.action.typeAnimation then
                                self:playAnimate(p.nodesByFace[a.target], p.animations[a.action], p.animationParams[a.action])
                                --print("triggerStateEvent animation", a.target, a.action)
                            elseif a.type == constant.action.typeStopAnimation then
                                self:stopAnimate(p.nodesByFace[a.target], p.animations[a.action])
                            elseif a.type == constant.action.typeAnimationFrame then
                                self:playAnimateByFrame(p.nodesByFace[a.target], p.animations[a.action], p.animationParams[a.action])
                            elseif a.type == constant.action.typeVisible then
                                p.nodesByFace[a.target]:setVisible(a.action)
                                --print("triggerStateEvent visible", a.target, a.action)
                            elseif a.type == constant.action.changeBackground then
                                self:setBackground(a.action)
                            elseif a.type == constant.action.changeForeground then
                                self:setForeground(a.action)
                            elseif a.type == constant.action.changeFace then
                                --print (" setFace", p.state ,a.action)
                                self:setFace(p, a.action)
                            elseif a.type == constant.action.playMusic then
                                self:playMusic(a.action)
                            elseif a.type == constant.action.stopMusic then
                                self:stopMusic(a.action)
                            elseif a.type == constant.action.startReplaceFace then
                                self:startFaceFilm(a.action, faceIndex)
                            elseif a.type == constant.action.endReplaceFace then
                                self:stopFaceFilm(a.action, faceIndex)
                            end
                        end

                        break
                    end
                end
                if bSwitch == true then
                    break
                end
            end
        end
    end
end

-- 触发某脸部物体事件
function PendantScene:triggerFaceEvent(event, faceIndex)
    for k1,p in pairs(self.pendents) do
        if k1 == faceIndex and self.faceRes ~= nil and self.faceRes[p.resIndex] ~= nil then

            -- face节点的event
            if self.faceRes[p.resIndex].events ~= nil then
                for t1, fe in pairs(self.faceRes[p.resIndex].events) do
                    if event == fe.event then
                        for t2, v1 in pairs(fe.actions) do
                            if v1.type == constant.action.typeAnimation then
                                if v1.target ~= nil then
                                    self:playAnimate(p.nodesByFace[v1.target], p.animations[v1.action], p.animationParams[v1.action])
                                end
                            elseif v1.type == constant.action.typeVisible then
                                --print ("run action", w.name, a.target, a.action,p.nodesByFace[a.target])
                                if v1.target ~= nil then
                                    p.nodesByFace[v1.target]:setVisible(v1.action)
                                end
                            elseif v1.type == constant.action.typeStopAnimation then
                                if v1.target ~= nil then
                                    self:stopAnimate(p.nodesByFace[v1.target], p.animations[v1.action])
                                end
                            elseif v1.type == constant.action.typeAnimationFrame then
                                if v1.target ~= nil then
                                    self:playAnimateByFrame(p.nodesByFace[v1.target], p.animations[v1.action], p.animationParams[v1.action])
                                end
                            elseif v1.type == constant.action.changeBackground then
                                self:setBackground(v1.action)
                            elseif v1.type == constant.action.changeForeground then
                                self:setForeground(v1.action)
                            elseif v1.type == constant.action.changeFace then
                                self:setFace(p, v1.action)
                            elseif v1.type == constant.action.playMusic then
                                self:playMusic(v1.action)
                            elseif v1.type == constant.action.stopMusic then
                                self:stopMusic(v1.action)
                            elseif v1.type == constant.action.startReplaceFace then
                                self:startFaceFilm(v1.action, faceIndex)
                            elseif v1.type == constant.action.endReplaceFace then
                                self:stopFaceFilm(v1.action, faceIndex)
                            end
                        end
                    end
                end
            end

            -- face上各元素的event
            for k1, w in pairs(self.faceRes[p.resIndex].widgetJsonArray) do
                if w ~= nil and w.events ~= nil then
                    for k2, e in pairs(w.events) do
                        if event == e.event then
                            for k3, a in pairs(e.actions) do
                                if a.type == constant.action.typeAnimation then
                                    print ("run action", w.name, a.target, a.action)
                                    if a.target == nil then
                                        self:playAnimate(p.nodesByFace[w.name], p.animations[a.action], p.animationParams[a.action])
                                        --self:playAnimateByFrame(p.nodesByFace[w.name], p.animations[a.action])
                                    else
                                        self:playAnimate(p.nodesByFace[a.target], p.animations[a.action], p.animationParams[a.action])
                                    end
                                elseif a.type == constant.action.typeStopAnimation then
                                    if a.target == nil then
                                        self:stopAnimate(p.nodesByFace[w.name], p.animations[a.action])
                                    else
                                        self:stopAnimate(p.nodesByFace[a.target], p.animations[a.action])
                                    end
                                elseif a.type == constant.action.typeAnimationFrame then
                                    if a.target == nil then
                                        self:playAnimateByFrame(p.nodesByFace[w.name], p.animations[a.action], p.animationParams[a.action])
                                    else
                                        self:playAnimateByFrame(p.nodesByFace[a.target], p.animations[a.action], p.animationParams[a.action])
                                    end

                                elseif a.type == constant.action.typeVisible then
                                    --print ("run action", w.name, a.target, a.action,p.nodesByFace[a.target])
                                    if a.target == nil then
                                        p.nodesByFace[w.name]:setVisible(a.action)
                                    else
                                        p.nodesByFace[a.target]:setVisible(a.action)
                                    end
                                elseif a.type == constant.action.changeBackground then
                                    self:setBackground(a.action)
                                elseif a.type == constant.action.changeForeground then
                                    self:setForeground(a.action)
                                elseif a.type == constant.action.changeFace then
                                    self:setFace(p, a.action)
                                elseif a.type == constant.action.playMusic then
                                    self:playMusic(a.action)
                                elseif a.type == constant.action.stopMusic then
                                    self:stopMusic(a.action)
                                elseif a.type == constant.action.startReplaceFace then
                                    self:startFaceFilm(a.action, faceIndex)
                                elseif a.type == constant.action.endReplaceFace then
                                    self:stopFaceFilm(a.action, faceIndex)
                                end
                            end
                        end
                    end
                end
            end
        end
    end
end

-- 所有脸部物体事件
function PendantScene:trigger(event)
    for k1,p in pairs(self.pendents) do
        for k1, w in pairs(self.faceRes[p.resIndex].widgetJsonArray) do
            if w ~= nil and w.events ~= nil then
                for k2, e in pairs(w.events) do
                    if event == e.event then

                        for k3, a in pairs(e.actions) do
                            if a.type == constant.action.typeAnimation then
                                --print ("run action", v.target, p.nodesByFace[v.target], v.action, p.animations, p.animations[v.action])
                                self:playAnimate(p.nodesByFace[w.name], p.animations[a.action], p.animationParams[a.action])
                            elseif a.type == constant.action.typeStopAnimation then
                                self:stopAnimate(p.nodesByFace[w.name], p.animations[a.action])
                            elseif a.type == constant.action.typeAnimationFrame then
                                self:playAnimateByFrame(p.nodesByFace[w.name], p.animations[a.action], p.animationParams[a.action])
                            elseif a.type == constant.action.typeVisible then
                                p.nodesByFace[w.name]:setVisible(a.action)
                            elseif a.type == constant.action.changeBackground then
                                self:setBackground(a.action)
                            elseif a.type == constant.action.changeForeground then
                                self:setForeground(a.action)
                            elseif a.type == constant.action.changeFace then
                                self:setFace(p, a.action)
                            elseif a.type == constant.action.playMusic then
                                self:playMusic(a.action)
                            elseif a.type == constant.action.stopMusic then
                                self:stopMusic(a.action)
                            --elseif a.type == constant.action.startReplaceFace then
                            --    self:replaceFace(a.action)
                            --elseif a.type == constant.action.endReplaceFace then
                            --    self:replaceFace("")
                            end
                        end
                    end
                end
            end
        end
    end
end

function PendantScene:createParticleFromPlist(v, dataplist)
    local plist = string.format("%s/%s", v.res, dataplist)
    local filePath =  cc.FileUtils:getInstance():fullPathForFilename(plist)
    local emitter = cc.ParticleSystemQuad:create(filePath)

    emitter:setVisible(v.visible)
    --emitter:setLocalZOrder(v.order)
    emitter:setGlobalZOrder(v.order)
--    if self.supportETC1 == false then
--        emitter:setBlendFunc(cc.blendFunc(ccb.BlendFactor.SRC_ALPHA , ccb.BlendFactor.ONE_MINUS_SRC_ALPHA))
--    end
    --emitter:setPositionZ(v.order)
    if v.position.x > 0 or v.position.y > 0 then
        emitter:setPosition(v.position.x, v.position.y)
    end
    if v.scale.x > 0 or v.scale.y > 0 then
        emitter:setScale(v.scale.x, v.scale.y)
    end
    if v.rotate > 0 then
        emitter:setRotation(v.rotate)
    end

    print("createParticleFromPlist filepath ", filePath, emitter)
    return emitter
end


function PendantScene:layoutGroundElements(sp , v)

    sp:setScaleX(v.scale.x)
    sp:setScaleY(v.scale.y)

    if v.horizontal_layout == constant.layout.left and v.vertical_layout ~= constant.layout.full then
        sp:setPositionX(0 + sp:getContentSize().width / 2 * v.scale.x)
    elseif v.horizontal_layout == constant.layout.right and v.vertical_layout ~= constant.layout.full then
        sp:setPositionX(self.width - sp:getContentSize().width / 2 * v.scale.x)
    elseif v.horizontal_layout == constant.layout.center then
        sp:setPositionX(self.width / 2)
    elseif v.horizontal_layout == constant.layout.full then
        sp:setPositionX(self.width/ 2)
        local rate = self.width * 1.0 / sp:getContentSize().width
        sp:setScaleX(rate)
        if v.name == "turn2" or v.name == "building" then
            print("self.width / sp:getContentSize().width", v.name, self.width, sp:getContentSize().width, rate)

        end
        if  v.vertical_layout ~= constant.layout.custom and v.vertical_layout ~= constant.layout.full then
            sp:setScaleY(rate)
            if v.vertical_layout == constant.layout.bottom then
                sp:setPositionY(0 + sp:getContentSize().height / 2 * rate)
            elseif v.vertical_layout == constant.layout.top then
                sp:setPositionY(self.height - sp:getContentSize().height / 2 * rate)
            end
        end

    elseif v.horizontal_layout == constant.layout.custom then
        if v.position.x == "random" then
            sp:setPositionX(math.random(0, self.width ))
        else
            sp:setPositionX(v.position.x * self.width)
        end
        if v.vertical_layout == constant.layout.custom then
            sp:setRotation(v.rotate)
        end
    end

    if v.vertical_layout == constant.layout.bottom  and v.horizontal_layout ~= constant.layout.full then
        sp:setPositionY(0 + sp:getContentSize().height / 2 * v.scale.y )
    elseif v.vertical_layout == constant.layout.top and v.horizontal_layout ~= constant.layout.full then
        sp:setPositionY(self.height - sp:getContentSize().height / 2 * v.scale.y)
    elseif v.vertical_layout == constant.layout.center then
        sp:setPositionY(self.height / 2)
    elseif v.vertical_layout == constant.layout.full then
        sp:setPositionY(self.height / 2)
        local rate = self.height * 1.0 / sp:getContentSize().height
        sp:setScaleY(rate)
        if v.name == "turn2" then
            print("self.height / sp:getContentSize().height", self.height, sp:getContentSize().height)

        end
        if  v.horizontal_layout ~= constant.layout.custom and v.horizontal_layout ~= constant.layout.full then
            sp:setScaleX(rate)
            if v.horizontal_layout == constant.layout.left then
                sp:setPositionX(0 + sp:getContentSize().width / 2 * rate)
            elseif v.horizontal_layout == constant.layout.right then
                sp:setPositionX(self.width - sp:getContentSize().width / 2 * rate)
            end
        end
    elseif v.vertical_layout == constant.layout.custom then

        if v.position.y == "random" then
            sp:setPositionY(math.random(0, self.height ))
        else
            sp:setPositionY(v.position.y * self.height)
        end

        if v.horizontal_layout == constant.layout.custom then
            sp:setRotation(v.rotate)
        end
    end
end

function PendantScene:createFilterMedia(params, type, info, startOrder)
    if params == nil then
        return
    end
    for k, v in pairs(params) do
        if v.type == type and info ~= nil then
            local sp = cc.Sprite:createWithTextureID(info.texID,
                    info.width, info.height)
            sp:setVisible(v.visible )
            sp:setOpacity(v.opacity)
            --sp:setLocalZOrder(startOrder + v.order)
            sp:setGlobalZOrder(startOrder + v.order)
            --sp:setPositionZ(v.order)
            if type == "media" then
--                sp:setBlendFunc(cc.blendFunc(ccb.BlendFactor.ONE , ccb.BlendFactor.ZERO))
            end
--            if self.supportETC1 == false then
--                sp:setBlendFunc(cc.blendFunc(ccb.BlendFactor.SRC_ALPHA , ccb.BlendFactor.ONE_MINUS_SRC_ALPHA))
--            end
            if v.blend ~= nil and constant.blend[v.blend] ~= nil and constant.blend[v.blend] ~= 0 then
                sp:setZegoBlendMode(constant.blend[v.blend])
            end

            sp:setAnchorPoint(cc.p(0.5, 0.5))
            self:layoutGroundElements(sp, v)

--            print("createFilterMedia  ", type)

            v.node = sp
            --self.scene:addChild(sp, startOrder + v.order)
            self.scene:addChild(sp)
--            print("media + v.order ", startOrder + v.order)
        end
    end
end

--background, foreground的element
function PendantScene:createElements(params, elementTable)

    local isBg = elementTable.isBg
    local startOrder = constant.order.background
    if isBg == false then
        startOrder = constant.order.foreground
    end

    if params == nil then
        return
    end
    -- 有多少动画
    local totalAnimNum = 0;
    for k, v in pairs(params) do
        local data = {}
        if v.res ~= nil  then
            data = self:parseJsonFile(string.format("%s/config.json", v.res))
            v.parsedata = data
            if data.animation ~= nil then
                if v.res ~= "ball" then
                    totalAnimNum = totalAnimNum + 1
                end
                v.animNum = totalAnimNum
            end
        end
    end

    for k, v in pairs(params) do

        local data = {}
        if v.res ~= nil then
            data = v.parsedata
--            data = self:parseJsonFile(string.format("%s/config.json", v.res))
            --print("config pic", data.pic)
        end

        if v.type == nil or v.type == "sprite" or v.type == "spriteEat" then
            local sp = cc.Sprite:create()
            local pic = string.format("%s/%s", v.res, data.pic)
            local filePath =  cc.FileUtils:getInstance():fullPathForFilename(pic)
            sp:setTexture(filePath)
            sp:setVisible(v.visible )
            sp:setOpacity(v.opacity)
            --sp:setLocalZOrder(v.order)
            sp:setGlobalZOrder(v.order)
            --sp:setPositionZ(v.order)
            if v.blend ~= nil and constant.blend[v.blend] ~= nil and constant.blend[v.blend] ~= 0 then
                sp:setZegoBlendMode(constant.blend[v.blend])
            end

            sp:setAnchorPoint(cc.p(0.5, 0.5))
--            if self.supportETC1 == false then
--                sp:setBlendFunc(cc.blendFunc(ccb.BlendFactor.SRC_ALPHA , ccb.BlendFactor.ONE_MINUS_SRC_ALPHA))
--            end

            self:layoutGroundElements(sp, v)
--            sp:setStretchEnabled(false)
--            sp:setContentSize(sp:getTexture():getContentSize())

            v.node = sp
            if v.type == "spriteEat" then
                elementTable.eats[v.name] = v
            end

            if v.res == "ball" then
                self.ball = sp
            end
            --v.type = "sprite"
            elementTable.elements[v.name] = v
--            print("ground + v.order ", startOrder + v.order)
            --self.scene:addChild(sp, startOrder + v.order)
            self.scene:addChild(sp)

        elseif v.type == "particle" then

            local particle = self:createParticleFromPlist(v, data.plist)
            if particle ~= nil then
                v.node = particle
                --v.type = "particle"
                elementTable.elements[v.name] = v
                print("createParticles, scene add")
                --self.scene:addChild(particle, startOrder + v.order)
                self.scene:addChild(particle)
            end
        elseif v.type == "media" or v.type == "segment" then
            elementTable.elements[v.name] = v

        elseif v.type == "video" then
            local videoplayer = cc.VideoPlayer:create()
            local video = string.format("%s/%s", v.res, data.video.file)
            local filePath =  cc.FileUtils:getInstance():fullPathForFilename(video)
            local filePathTexture =  cc.FileUtils:getInstance():fullPathForFilename(v.texture)
            videoplayer:initPlayer(0, v.video_width, v.video_height)

            if data.video.loop ~= nil then
                videoplayer:setLooping(data.video.loop)
            end
            --videoplayer:setTexture(filePathTexture)
            videoplayer:setFileName(filePath)
            videoplayer:setAlphaMode(v.alpha_mode)
--            if self.supportETC1 == false then
--                videoplayer:setBlendFunc(cc.blendFunc(ccb.BlendFactor.SRC_ALPHA , ccb.BlendFactor.ONE_MINUS_SRC_ALPHA))
--            end
            if v.blend ~= nil and constant.blend[v.blend] ~= nil and constant.blend[v.blend] ~= 0 then
                videoplayer:setZegoBlendMode(constant.blend[v.blend])
            end

            videoplayer:play()

            self:layoutGroundElements(videoplayer, v)

            videoplayer:setVisible(v.visible)
            --videoplayer:setOpacity(v.opacity)
            --videoplayer:setLocalZOrder(v.order)
            videoplayer:setGlobalZOrder(v.order)
            v.node = videoplayer
            elementTable.elements[v.name] = v
            --self.scene:addChild(videoplayer, startOrder + v.order)
            self.scene:addChild(videoplayer)
        end

        if data.animation ~= nil then
            local isLast = false
            if v.animNum == totalAnimNum then
                isLast = true
            end
--            print("createGroundAnims ", k, totalAnimNum, isLast)
            self:createGroundAnims(data.animation, v.res, isLast)
        end

    print(" createElements ",   v.name, elementTable.elements[v.name])
    end
end

function PendantScene:initBackgrounds(params)
--    print("background start")
    for k, v in pairs(params) do
        local background = {}
        background.name = v.name
        background.bg_index = v.bg_index
        background.default = v.default
        background.elementJsonArray = v.element
        background.isBg = true

        background.elements  = {}
        background.eats  = {}

        if background.default == true then
            self:createElements(v.element, background)
            self.curBg = background
        end
        self.bgs[background.name] = background
    end
end


function PendantScene:playAnimateByFrame(sprite, animate, param)

    if sprite ~= nil and animate ~= nil then
        local total = #(animate:getAnimation():getFrames())
        local frames = animate:getAnimation():getFrames()

        if param.tmpFrame < total then
            sprite:setSpriteFrame(frames[param.tmpFrame % total + 1]:getSpriteFrame())
            param.tmpFrame = param.tmpFrame + 1
        end
    end
end

function PendantScene:playAnimate(node, animate, param)
    if node ~= nil and animate ~= nil and param ~= nil then

        --print( "getCurrentFrameIndex", animate:getCurrentFrameIndex() , #(animate:getAnimation():getFrames()), param.loop , animate:isDone())
        --if animate:getCurrentFrameIndex() <= 0 then
        --    node:stopAllActions()
        --    node:runAction(animate)
        --    --elseif param.loop  ~= -1 and animate:getCurrentFrameIndex() >= #(animate:getAnimation():getFrames()) - 1 then
        --else
        if animate:getCurrentFrameIndex() <= 0 or animate:isDone() then
            print("playAnimation, ", node, animate, param)
            node:stopAllActions()
            node:runAction(animate)
        end

    end
end

function PendantScene:stopAnimate(node, animate)
    if node ~= nil and animate ~= nil then
        node:stopAllActions()
    end
end

function PendantScene:triggerGroundEvent(event, isBackGround)
    local ground = self.curFg
    if isBackGround then
        ground = self.curBg
    end
    if ground ~= nil then
        for name, element in pairs(ground.elements) do

            if element ~= nil and element.events ~= nil and element.node ~= nil then
                for k2, e in pairs(element.events) do
                    if event == e.event then
                        for k3, a in pairs(e.actions) do
                            if a.type == constant.action.typeAnimation then
--                                print ("run ground action", event, element.name, a.action, element.node, self.animations[a.action], self.animations[a.action]:isDone())
                                self:playAnimate(element.node, self.animations[a.action], self.animationParams[a.action])
                            elseif a.type == constant.action.typeStopAnimation then
                                self:stopAnimate(element.node, self.animations[a.action])
                            elseif a.type == constant.action.typeAnimationFrame then
                                self:playAnimateByFrame(element.node, self.animations[a.action], self.animationParams[a.action])
                            elseif a.type == constant.action.typeVisible then
                                element.node:setVisible(a.action)
                            elseif a.type == constant.action.changeBackground then
                                self:setBackground(a.action)
                            elseif a.type == constant.action.changeForeground then
                                self:setForeground(a.action)
                            elseif a.type == constant.action.changeFace then
                                self:setFace(p, a.action)
                            elseif a.type == constant.action.playMusic then
                                self:playMusic(a.action)
                            elseif a.type == constant.action.stopMusic then
                                self:stopMusic(a.action)
                            end
                        end
                    end
                end
            end
        end
    end
end

function PendantScene:removeBackground()
    if self.curBg ~= nil then
        if self.curBg.elements ~= nil then
            for k, v in pairs(self.curBg.elements) do
                self.scene:removeChild(v.node, true)
            end
        end
    end
end

function PendantScene:setBackground(name)
    if self.bgs[name] == nil then
        print("Background res is nil, name:", name)
        return
    end
    print("Background ", name)
    self:removeBackground()
    self.curBg = nil

    local background = self.bgs[name]
    self:createElements(background.elementJsonArray, background)
    self.curBg = background
    if self.curBg ~= nil then
        self:createFilterMedia(self.curBg.elements, "media", self.mediaSprite, constant.order.background)
        self:createFilterMedia(self.curBg.elements, "segment", self.mediaSegment, constant.order.background)
    end

    self:triggerGroundEvent(constant.event.sceneBegin, true)
end

function PendantScene:removeForeground()
    if self.curFg ~= nil then
        if self.curFg.elements ~= nil then
            for k, v in pairs(self.curFg.elements) do
                self.scene:removeChild(v.node, true)
            end
        end
    end
end

function PendantScene:setForeground(name)
    if self.fgs[name] == nil then
        print("Foreground res is nil, name:", name)
        return
    end
    print("Foreground ", name)
    self:removeForeground()
    self.curFg = nil

    local foreground = self.fgs[name]
    self:createElements(foreground.elementJsonArray, foreground)
    self.curFg = foreground
    if self.curFg ~= nil then
        self:createFilterMedia(self.curFg.elements, "media", self.mediaSprite, constant.order.foreground)
        self:createFilterMedia(self.curFg.elements, "segment", self.mediaSegment, constant.order.foreground)
    end
    self:triggerGroundEvent(constant.event.sceneBegin, false)
end

function PendantScene:initForegrounds(params)
    for k, v in pairs(params) do
        local foreground = {}
        foreground.name = v.name
        foreground.fg_index = v.fg_index
        foreground.default = v.default
        foreground.elementJsonArray = v.element
        foreground.isBg = false
        foreground.elements = {}
        foreground.eats = {}

        if foreground.default == true then
            self:createElements(foreground.elementJsonArray, foreground)
            self.curFg = foreground
        end

        self.fgs[foreground.name] = foreground
    end
end

function PendantScene:initFaceRes(params)
    for k, v in pairs(params) do
        local face = {}
        face.name = v.name
        face.res_index = v.res_index
        face.default = v.default

        face.widgetJsonArray = v.widget
        --face.widgets = {}
        face.used = false
        face.events = v.events

        --if face.default == true then
        --    face.used = true
        --    self:createWidgets(v.widget, face)
        --end

        face.replace_texture = v.replace_texture

        self.faceRes[v.res_index] = face

        print( "res face ",v.res_index, face.name)
    end
end

function PendantScene:createMusicAudio()
    local str = "music/config.json"
    if cc.FileUtils:getInstance():isFileExist(str) == true then
        local jsonData = self:parseJsonFile(str)
        if jsonData.music ~= nil then
            for k, v in pairs(jsonData.music) do
                v.res = string.format("music/%s", v.res)
                self.musics[v.name] = v
                self.musics[v.name].id = -1
            end
        end
    end
end


function PendantScene:createFaceFilm()
    local str = "facefilm/config.json"
    if cc.FileUtils:getInstance():isFileExist(str) == true then
        local jsonData = self:parseJsonFile(str)
        if jsonData.faceFilm ~= nil then
            for k, v in pairs(jsonData.faceFilm) do
                local film = {}
                local resArray = {}

                film.name = v.name
                film.fps = v.fps
                film.loop = v.loop
                film.blend = v.blend
                film.type = v.type
                film.id = v.id
                film.trigger_times = v.trigger_times
                for i = 0, v.num do
                    local str = string.format("facefilm/%s%02d%s", v.res, i, v.suffix)
                    if cc.FileUtils:getInstance():isFileExist(str) == true then
                        resArray[i + 1] = str
                    end
                    if resArray[i + 1] == nil then
                        str = string.format("facefilm/%s%03d%s", v.res, i, v.suffix)
                        if cc.FileUtils:getInstance():isFileExist(str) == true then
                            resArray[i + 1] = str
                        end
                    end
                end
                film.resArray = resArray
                self.faceFilms[v.name] = film

                --目前facefilm先取第一个
                if self.kiwiEngine.faceFilm ~= nil and (self.faceFilms[v.name].type == nil
                        or self.faceFilms[v.name].type == "default") then
                    self.kiwiEngine.faceFilm.setTargets(
                            self.faceFilms[v.name].id,
                            self.faceFilms[v.name].resArray,
                            self.faceFilms[v.name].fps)
                elseif self.kiwiEngine.uvFaceFilm ~= nil and self.faceFilms[v.name].type == "uv" then
                    self.kiwiEngine.uvFaceFilm.setTargets(
                            self.faceFilms[v.name].id,
                            self.faceFilms[v.name].resArray,
                            self.faceFilms[v.name].fps)
                end

                --print( "film blend ", constant.blend[self.faceFilms[v.name].blend])
                if self.kiwiEngine.faceFilm ~= nil and (self.faceFilms[v.name].type == nil
                        or self.faceFilms[v.name].type == "default") then
                    if constant.blend[self.faceFilms[v.name].blend] ~= nil then
                        self.kiwiEngine.faceFilm.setBlendType(
                                self.faceFilms[v.name].id,
                                constant.blend[self.faceFilms[v.name].blend])
                    end
                elseif self.kiwiEngine.uvFaceFilm ~= nil and (self.faceFilms[v.name].type == nil
                        or self.faceFilms[v.name].type == "uv") then
                    if constant.blend[self.faceFilms[v.name].blend] ~= nil then
                        self.kiwiEngine.uvFaceFilm.setBlendType(
                                self.faceFilms[v.name].id,
                                constant.blend[self.faceFilms[v.name].blend])
                    end
                end


            end


        end
    end
end

function PendantScene:playMusic(name)
    local m = self.musics[name]
    if m ~= nil then
        if m.id == -1 or cc.AudioEngine:getState(m.id) ~= 1 then
            local path = cc.FileUtils:getInstance():fullPathForFilename(m.res)
            local id = cc.AudioEngine:play2d(path, m.loop, m.volume, nil)
            print("playMusic ", name, path, m.loop, m.volume, id)
            self.musics[name].id = id

            --self:replaceFace("leaf_3faceFilm", 1)
        else
            print("no need playMusic", name)
        end
    end
end

function PendantScene:stopMusic(name)
    local m = self.musics[name]
    if m ~= nil and m.id >= 0 then
        cc.AudioEngine:stop(m.id)
        print("stopMusic ", name, m.id )
        self.musics[name].id = -1
    else
        print("no need stopMusic", name)
    end
end


function addImageCallback(tex)
    print(" addImageCallback tex ok, " , tex)
end

function PendantScene:createGroundAnimBall(plist, vv, ss)
    cc.SpriteFrameCache:getInstance():addSpriteFrames(plist)
    local animFrames = { }

    for i = 0, vv.num do
        local str = string.format("%s%02d.png", vv.res, i)
        if cc.SpriteFrameCache:getInstance():getSpriteFrame(str) ~= nil then
            animFrames[i] = cc.SpriteFrameCache:getInstance():getSpriteFrame(str);
        end

        if animFrames[i] == nil then
            str = string.format("%s%03d.png", vv.res, i)
            if cc.SpriteFrameCache:getInstance():getSpriteFrame(str) ~= nil then
                animFrames[i] = cc.SpriteFrameCache:getInstance():getSpriteFrame(str);
            end
        end
    end

    local animation = cc.Animation:createWithSpriteFrames(animFrames, vv.interval)

    --创建Animation (帧table，播放间隔 )
    if vv.loop ~= nil and  vv.loop < 0 then
        animation:setLoops(999999)
    else
        animation:setLoops(vv.loop)
    end

    animation:setDelayPerUnit(vv.interval)
    --创建Animate
    local animate = cc.Animate:create(animation)
    animate:retain()
    ss.animations[vv.name] = animate
    vv.tmpFrame = 0
    ss.animationParams[vv.name] = vv

    print("scene animate ", vv.name, ss. animations[vv.name])

end

function PendantScene:createGroundAnims(params, dir, isLast)

    local bLast = isLast
    if params == nil then
        return
    end
    for k, v in pairs(params) do
        self.hasGroudAnim = true
        if self.supportETC1 == true then
            local plist = string.format("%s/%s.plist", dir, v.res)
            local pkm = string.format("%s/%s.pkm", dir, v.res)
--            cc.SpriteFrameCache:getInstance():addSpriteFrames(plist)
            local vv = v
            local ss = self

            if v.res ~= "ball" then
                cc.Director:getInstance():getTextureCache():addImageAsync(pkm, function(tex)
                    cc.SpriteFrameCache:getInstance():addSpriteFrames(plist)
                    local animFrames = { }

                    for i = 0, vv.num do
                        local str = string.format("%s%02d.png", vv.res, i)
                        if cc.SpriteFrameCache:getInstance():getSpriteFrame(str) ~= nil then
                            animFrames[i] = cc.SpriteFrameCache:getInstance():getSpriteFrame(str);
                        end

                        if animFrames[i] == nil then
                            str = string.format("%s%03d.png", vv.res, i)
                            if cc.SpriteFrameCache:getInstance():getSpriteFrame(str) ~= nil then
                                animFrames[i] = cc.SpriteFrameCache:getInstance():getSpriteFrame(str);
                            end
                        end
                    end

                    local animation = cc.Animation:createWithSpriteFrames(animFrames, vv.interval)

                    --创建Animation (帧table，播放间隔 )
                    if vv.loop ~= nil and  vv.loop < 0 then
                        animation:setLoops(999999)
                    else
                        animation:setLoops(vv.loop)
                    end

                    animation:setDelayPerUnit(vv.interval)
                    --创建Animate
                    local animate = cc.Animate:create(animation)
                    animate:retain()
                    ss.animations[vv.name] = animate
                    vv.tmpFrame = 0
                    ss.animationParams[vv.name] = vv

                    print("scene animate ", vv.name, ss. animations[vv.name])
                    if bLast  == true then
                        print("trigger sceneBegin animate ")
                        ss:trigger(constant.event.sceneBegin)
                        ss:triggerGroundEvent(constant.event.sceneBegin, false)
                        ss:triggerGroundEvent(constant.event.sceneBegin, true)
                    end
                end)
            else
                self:createGroundAnimBall(plist, vv, ss)
            end

        else
            local animation = cc.Animation:create()
            local p = string.format("%s/%s", dir, v.res)
            local path =  cc.FileUtils:getInstance():fullPathForDirectory(dir)
--            print("animaion file path", dir, path)
            local files =  cc.FileUtils:getInstance():listFiles(path)
            table.sort(files)
            for k,v in pairs(files) do
                if k ~= 1 and k ~= 2 and cc.FileUtils:getInstance():getFileExtension(v) == ".png" then  --去掉 . 和 ..
--                    print ("addSpriteFrameWithFile = ", cc.FileUtils:getInstance():getFileExtension(v))
--                    print ("addSpriteFrameWithFile = ", v)
                    animation:addSpriteFrameWithFile(v);
--                    print ("addSpriteFrameWithFile = ", v, v.name)
                end
            end

            if v.loop ~= nil and  v.loop < 0 then
                animation:setLoops(999999)
            else
                animation:setLoops(v.loop)
            end
            --创建Animation (帧table，播放间隔 )
            --animation:setLoops(v.loop)
            animation:setDelayPerUnit(v.interval)
            --创建Animate
            local animate = cc.Animate:create(animation)
            animate:retain()
            self.animations[v.name] = animate
            v.tmpFrame = 0
            self.animationParams[v.name] = v
        end
--        print("scene animate ", v.name, self. animations[v.name])
    end
end

function PendantScene:parseJsonFile(file)
    local filePath =  cc.FileUtils:getInstance():fullPathForFilename(file)

    print("PendantScene parseJsonFile start ", filePath)
    local f = io.open(filePath, "r" )
    if f ~= nil then
        local t = f:read( "*all" )
        f:close()
        if nil ~= t and "" ~= t then
            local jsonData = json.decode(t, 1)
            if jsonData == nil then
                print("PendantScene Json error")
            end

            return jsonData
        else
            print("PendantScene data was empty")
        end
    end
    return nil
end

function PendantScene:parsePendantJsonFile(file)
    local jsonData = self:parseJsonFile(file)
    if jsonData ~= nil then
        for k,v in pairs(jsonData) do
            if k == constant.json.background then
                self.backgroundJsonArray = v
                --self:initBackgrounds(v)
            elseif k == constant.json.foreground then
                self.foregroundJsonArray = v
                --self:initForegrounds(v)
            elseif k == constant.json.face then
                self.faceJsonArray = v
                --self:initFaceRes(v)
            elseif k == constant.json.face_order then
                self.faceOrder = v
            elseif k == constant.json.animation then
                self.animationJsonArray = v
                --self:createAnims(v)
            elseif k == constant.json.minEngineVersion then
                self.minEngineVersion = v
            elseif k == constant.json.version then
                self.version = v
            elseif k == constant.json.debug then
                self.debug = v
            elseif k == constant.json.type then
                self.type = v
            elseif k == constant.json.state then
                self.stateJson = v
            elseif k == constant.json.ground_animation then
                self.groundAnimationJsonArray = v

                --elseif k == constant.json.aiSegment then
                --    self.aiSegmentJsonArray = v
            elseif k == constant.json.supportETC1 then
                self.supportETC1 = v

            end
        end
        self:initBackgrounds(self.backgroundJsonArray)
        self:initForegrounds(self.foregroundJsonArray)
        self:initFaceRes(self.faceJsonArray)
        self:createMusicAudio()
        self:createFaceFilm()

        print ("version info :" , self.minEngineVersion, self.version, self.type, self.debug, self.supportETC1)
    else
        print("pendant data was empty ")
    end
end

function PendantScene:stopFaceFilm(faceFilmName, faceIndex)

    if self.faceFilms[faceFilmName].type == "uv" then
        self.kiwiEngine.uvFaceFilm.stop(faceIndex, self.faceFilms[faceFilmName].id)
    elseif self.faceFilms[faceFilmName].type == "default" then
        self.kiwiEngine.faceFilm.stop(faceIndex, self.faceFilms[faceFilmName].id)
    end
end

function PendantScene:startFaceFilm(faceFilmName, faceIndex)

    print("startFaceFilm ", faceFilmName)
    if self.faceFilms[faceFilmName] ~= nil then
        if self.faceFilms[faceFilmName].trigger_times == nil or self.faceFilms[faceFilmName].trigger_times < 0 or self.faceFilms[faceFilmName].trigger_times > 0 then
            --print("startFaceFilm trigger_times", self.faceFilms[faceFilmName].trigger_times)
            if self.faceFilms[faceFilmName].trigger_times ~= nil and self.faceFilms[faceFilmName].trigger_times > 0 then
                self.faceFilms[faceFilmName].trigger_times = self.faceFilms[faceFilmName].trigger_times - 1
            end
            if self.faceFilms[faceFilmName].type == "uv" then
--                print("startFaceFilm uv", faceFilmName)
                self.kiwiEngine.uvFaceFilm.start(faceIndex, self.faceFilms[faceFilmName].id, self.faceFilms[faceFilmName].loop)
            elseif self.faceFilms[faceFilmName].type == "default" then
                self.kiwiEngine.faceFilm.start(faceIndex, self.faceFilms[faceFilmName].id, self.faceFilms[faceFilmName].loop)
            end
        else
            print("startFaceFilm  no need, trigger_times is 0")
        end

    end
end

function PendantScene:finish()
    self:trigger(constant.event.sceneEND)

    for k, v in pairs(self.animations) do
        v:release()
    end
    cc.AudioEngine:stopAll()

    for k,v in pairs(self.pendents) do
        if v ~= nil then
            v:finish()
            v:onFinish()
            v = nil
        end
    end

    self:removeBackground()
    self:removeForeground()

--    cc.Director:getInstance():popScene()
--    self:scene:release()
    cc.Director:getInstance():purgeCachedData()

    print("finish Scene")

end

return PendantScene
