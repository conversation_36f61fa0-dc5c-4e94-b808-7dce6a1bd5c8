import 'package:shortie/utils/enums.dart';

final Map<String, String> ruRU = {
  EnumLocal.txtAppName.name: "Коротышка",
  EnumLocal.txtSkip.name: "Пропускать",
  EnumLocal.txtOnBoardingTitle_1.name: "Лучшее приложение века для социальных сетей",
  EnumLocal.txtOnBoardingTitle_2.name: "Давайте общаться со всеми в мире",
  EnumLocal.txtOnBoardingTitle_3.name: "Все, что вы можете сделать в приложении",
  EnumLocal.txtOnBoardingSubTitle_1.name: "Откройте для себя будущее социальных сетей благодаря нашим передовым функциям и интуитивно понятному дизайну. Будьте готовы общаться, делиться и делать открытия, как никогда раньше!",
  EnumLocal.txtOnBoardingSubTitle_2.name: "Присоединяйтесь к глобальному сообществу, которое является страстным, разнообразным и ярким. Общайтесь с другими, делитесь своей историей и делайте мир меньше и прекраснее!",
  EnumLocal.txtOnBoardingSubTitle_3.name: "Наше приложение поможет вам: от обмена жизненными моментами до открытия новых интересов. Исследуйте, создавайте и процветайте в мире безграничных возможностей!",
  EnumLocal.txtLoginTitle.name: "Давайте познакомимся с новыми людьми вокруг вас",
  EnumLocal.txtLoginSubTitle.name: "Добро пожаловать! Быстро войдите в систему или используйте свою учетную запись Google, чтобы начать знакомиться с новыми людьми вокруг вас. Давайте начнем ваше путешествие к новым знакомствам!",
  EnumLocal.txtQuickLogIn.name: "Быстрый вход",
  EnumLocal.txtContinueWithGoogle.name: "Продолжить с Google",
  EnumLocal.txtFillProfile.name: "Заполнить профиль",
  EnumLocal.txtEditProfile.name: "Редактировать профиль",
  EnumLocal.txtFullName.name: "Полное имя",
  EnumLocal.txtUserName.name: "Имя пользователя",
  EnumLocal.txtIdentificationCode.name: "Идентификационный код",
  EnumLocal.txtCountry.name: "Страна",
  EnumLocal.txtBioDetails.name: "Биография",
  EnumLocal.txtGender.name: "Пол",
  EnumLocal.txtMale.name: "Мужской",
  EnumLocal.txtFemale.name: "Женский",
  EnumLocal.txtOther.name: "Другой",
  EnumLocal.txtSaveProfile.name: "Сохранить профиль",
  EnumLocal.txtSendGift.name: "Послать подарок",
  EnumLocal.txtSend.name: "Отправлять",
  EnumLocal.txtCoins.name: "Монеты",
  EnumLocal.txtRecharge.name: "Перезарядка",
  EnumLocal.txtComment.name: "Комментарий",
  EnumLocal.txtTypeComment.name: "Введите комментарий...",
  EnumLocal.txtVideos.name: "Видео",
  EnumLocal.txtNext.name: "Следующий",
  EnumLocal.txtImages.name: "Изображений",
  EnumLocal.txtAddMusic.name: "Добавить музыку",
  EnumLocal.txtSearchHintText.name: "Поиск...",
  EnumLocal.txtDiscover.name: "Обнаружить",
  EnumLocal.txtFavourite.name: "Любимый",
  EnumLocal.txtPreview.name: "Предварительный просмотр",
  EnumLocal.txtUploadReels.name: "Загрузить ролики",
  EnumLocal.txtWhatsOnYourMind.name: "Что у тебя на уме?",
  EnumLocal.txtChangeThumbnail.name: "Изменить миниатюру",
  EnumLocal.txtAddTopic.name: "Добавить тему",
  EnumLocal.txtUpload.name: "Загрузить",
  EnumLocal.txtAddHashtag.name: "Добавить хештег",
  EnumLocal.txtDone.name: "Сделанный",
  EnumLocal.txtLiveStreaming.name: "Прямая трансляция",
  EnumLocal.txtGoLive.name: "В прямом эфире",
  EnumLocal.txtLive.name: "Жить",
  EnumLocal.txtStopLive.name: "Остановить жить",
  EnumLocal.txtStop.name: "Останавливаться",
  EnumLocal.txtStopLiveDialogText.name: "Остановить прямую трансляцию? Это действие нельзя отменить, оно немедленно прекратит трансляцию. Нажмите «Остановить трансляцию» для подтверждения.",
  EnumLocal.txtFeeds.name: "Ленты",
  EnumLocal.txtCreate.name: "Создавать",
  EnumLocal.txtSayHi.name: "Скажи привет",
  EnumLocal.txtReport.name: "Отчет",
  EnumLocal.txtCancel.name: "Отмена",
  EnumLocal.txtItIsSpam.name: "Это спам",
  EnumLocal.txtNudityOrSexualActivity.name: "Нагота или сексуальная активность",
  EnumLocal.txtHateSpeechOrSymbols.name: "Разжигание ненависти или символы",
  EnumLocal.txtViolenceOrDangerousOrganization.name: "Насилие или опасная организация",
  EnumLocal.txtFalseInformation.name: "Ложная информация",
  EnumLocal.txtBullyingOrHarassment.name: "Запугивание или преследование",
  EnumLocal.txtScamOrFraud.name: "Мошенничество или мошенничество",
  EnumLocal.txtIntellectualPropertyViolation.name: "Нарушение интеллектуальной собственности",
  EnumLocal.txtSuicideOrSelfInjury.name: "Самоубийство или членовредительство",
  EnumLocal.txtDrugs.name: "Наркотики",
  EnumLocal.txtEatingDisorders.name: "Расстройства пищевого поведения",
  EnumLocal.txtSomethingElse.name: "Что-то другое",
  EnumLocal.txtChildAbuse.name: "Жестокое обращение с ребенком",
  EnumLocal.txtOthers.name: "Другие",
  EnumLocal.txtUploadPost.name: "Загрузить сообщение",
  EnumLocal.txtSearch.name: "Поиск",
  EnumLocal.txtHashTag.name: "Хэштег",
  EnumLocal.txtViewAll.name: "Посмотреть все",
  EnumLocal.txtScanQRCode.name: "Сканировать QR-код",
  EnumLocal.txtScanQRCodeText.name: "Сканируйте QR-код, чтобы увидеть профиль",
  EnumLocal.txtMyQRCodeText.name: "Добро пожаловать на мою страницу с QR-кодом! Сканируя QR-код выше, вы мгновенно получите доступ к данным моего профиля. Сюда входит моя контактная информация, профили в социальных сетях и другие важные данные, которые помогут вам легко связаться со мной. Просто наведите камеру своего смартфона на QR-код, и вы будете перенаправлены на мою персональную страницу профиля. Оставайтесь на связи и узнайте обо мне больше с помощью всего лишь быстрого сканирования!",
  EnumLocal.txtViewProfile.name: "Просмотреть профиль",
  EnumLocal.txtViewDetails.name: "Посмотреть детали",
  EnumLocal.txtFollow.name: "Следовать",
  EnumLocal.txtFollowing.name: "Следующий",
  EnumLocal.txtLikes.name: "Нравится",
  EnumLocal.txtFollowers.name: "Последователи",
  EnumLocal.txtReels.name: "Катушки",
  EnumLocal.txtCollections.name: "Коллекции",
  EnumLocal.txtSettings.name: "Настройки",
  EnumLocal.txtAccount.name: "Счет",
  EnumLocal.txtNotifyMe.name: "Сообщите мне",
  EnumLocal.txtLanguages.name: "Языки",
  EnumLocal.txtMyWallet.name: "Мой бумажник",
  EnumLocal.txtShareProfile.name: "Поделиться профилем",
  EnumLocal.txtMyQRCode.name: "Мой QR-код",
  EnumLocal.txtVerificationRequest.name: "Запрос на проверку",
  EnumLocal.txtGeneral.name: "Общий",
  EnumLocal.txtHelp.name: "Помощь",
  EnumLocal.txtTermsOfUse.name: "Условия эксплуатации",
  EnumLocal.txtPrivacyPolicy.name: "политика конфиденциальности",
  EnumLocal.txtLogOut.name: "Выйти",
  EnumLocal.txtDeleteAccount.name: "Удалить аккаунт",
  EnumLocal.txtDelete.name: "Удалить",
  EnumLocal.txtLogOutText.name: "Вы собираетесь выйти из своей учетной записи. Вы будете перенаправлены на страницу входа. Вы уверены что хотите продолжить?",
  EnumLocal.txtDeleteAccountText.name: "Вы уверены, что хотите навсегда удалить свою учетную запись? Это действие невозможно отменить, оно приведет к потере всех ваших данных и доступа к связанным службам.",
  EnumLocal.txtUploadYourImages.name: "Загрузите свои изображения",
  EnumLocal.txtUploadIDPhotos.name: "Загрузить фотографии на документы",
  EnumLocal.txtPersonalPhotos.name: "Личные фотографии",
  EnumLocal.txtClearPhotos.name: "Четкие фотографии",
  EnumLocal.txtCapture.name: "Захватывать",
  EnumLocal.txtAttach.name: "Прикреплять",
  EnumLocal.txtIDNumber.name: "Идентификационный номер",
  EnumLocal.txtNameOnID.name: "Имя в удостоверении личности",
  EnumLocal.txtFullAddress.name: "Полный адрес",
  EnumLocal.txtSubmit.name: "Представлять на рассмотрение",
  EnumLocal.txtAvailableCoinBalance.name: "Доступный баланс монет",
  EnumLocal.txtRechargeCoin.name: "Пополнить монету",
  EnumLocal.txtCoinHistory.name: "История монет",
  EnumLocal.txtWithdraw.name: "Отзывать",
  EnumLocal.txtAvailableCoin.name: "Доступная монета",
  EnumLocal.txtEnterCoin.name: "Введите монету",
  EnumLocal.txtEnterWithdrawCoin.name: "Введите вывод монеты...",
  EnumLocal.txtMinimumWithdraw.name: "*Минимальный вывод",
  EnumLocal.txtSelectPaymentGateway.name: "Выберите платежный шлюз",
  EnumLocal.txtSelectPaymentMethod.name: "Выберите метод оплаты",
  EnumLocal.txtPurchasePremiumPlanAndGetAllAccess.name: "Купите Премиум-план и получите полный доступ",
  EnumLocal.txtRechargePageSubTitle.name: "Перейдите на Премиум-план, чтобы получить неограниченный доступ к расширенным инструментам и эксклюзивным ресурсам. Повысьте свой опыт прямо сейчас.",
  EnumLocal.txtConfirmWithdrawDialogText.name: "Пожалуйста, внимательно проверьте детали вывода средств, чтобы обеспечить точность. Дважды проверьте информацию о целевом счете и сумму вывода, чтобы избежать ошибок.",
  EnumLocal.txtPurchasePlan.name: "План покупки",
  EnumLocal.txtPayNow.name: "Заплатить сейчас",
  EnumLocal.txtMostPopularPlan.name: "Самый популярный план",
  EnumLocal.txtComplaintOrSuggestion.name: "Жалоба или предложение",
  EnumLocal.txtTypingSomethings.name: "Ввод чего-либо...",
  EnumLocal.txtContact.name: "Контакт",
  EnumLocal.txtAttachYourImageOrScreenshot.name: "Прикрепите свое изображение или скриншот",
  EnumLocal.txtBrowse.name: "Просматривать",
  EnumLocal.txtNone.name: "Никто",
  EnumLocal.txtChooseImage.name: "Выберите изображение",
  EnumLocal.txtGallery.name: "Галерея",
  EnumLocal.txtTakePhoto.name: "Сделать фотографию",
  EnumLocal.txtChooseVideo.name: "Выбрать видео",
  EnumLocal.txtCreateReels.name: "Создать барабаны",
  EnumLocal.txtNoDataFound.name: "Данные не найдены !!",
  EnumLocal.txtTypeSomething.name: "Введите что-либо...",
  EnumLocal.txtSomeThingWentWrong.name: "Что-то пошло не так !!",
  EnumLocal.txtConnectionLost.name: "Соединение потеряно !!",
  EnumLocal.txtNoInternetConnection.name: "Нет соединения с интернетом !!",
  EnumLocal.txtLoginSuccess.name: "Успех входа в систему",
  EnumLocal.txtProfileUpdateSuccessfully.name: "Профиль обновлен успешно",
  EnumLocal.txtReelsUploadSuccessfully.name: "Ролики успешно загружены",
  EnumLocal.txtYouCantFollowYourOwnAccount.name: "Вы не можете подписаться на свой собственный аккаунт",
  EnumLocal.txtReportSending.name: "Отправка отчета...",
  EnumLocal.txtReportSendSuccess.name: "Отчет об успешной отправке",
  EnumLocal.txtYouCanSelectMaximumFiveImages.name: "Вы можете выбрать максимум 5 изображений!",
  EnumLocal.txtPostUploading.name: "Загрузка сообщения...",
  EnumLocal.txtPostUploadSuccessfully.name: "Опубликовать загрузку успешно",
  EnumLocal.txtPleaseSelectProfileImage.name: "Пожалуйста, выберите изображение профиля !!",
  EnumLocal.txtPleaseEnterFullName.name: "Пожалуйста, введите полное имя!!",
  EnumLocal.txtPleaseEnterUserName.name: "Пожалуйста, введите имя пользователя !!",
  EnumLocal.txtPleaseEnterBioDetails.name: "Пожалуйста, введите биографические данные !!",
  EnumLocal.txtPleaseAllowPermission.name: "Пожалуйста, разрешите!!",
  EnumLocal.txtPleaseWaitSomeTime.name: "Пожалуйста, подождите когда-нибудь...",
  EnumLocal.txtPleaseEnterCaption.name: "Пожалуйста, введите подпись !!",
  EnumLocal.txtPleaseSelectHashTag.name: "Пожалуйста, выберите хэш-тег !!",
  EnumLocal.txtPleaseSelectPost.name: "Пожалуйста, выберите публикацию !!",
  EnumLocal.txtPleaseEnterWithdrawCoin.name: "Пожалуйста, введите вывод монеты !!",
  EnumLocal.txtPleaseSelectWithdrawMethod.name: "Пожалуйста, выберите способ вывода средств !!",
  EnumLocal.txtPleaseEnterAllPaymentDetails.name: "Пожалуйста, введите все платежные реквизиты!",
  EnumLocal.txtCoinNotAvailableForSendGiftText.name: "У вас недостаточно средств для отправки подарка!!",
  EnumLocal.txtWithdrawalRequestedCoinMustBeGreaterThanSpecifiedByTheAdmin.name: "Запрошенная монета на вывод должна быть больше, чем указано администратором!!",
  EnumLocal.txtDownloadSuccess.name: "Скачать Успех",
  EnumLocal.txtPleaseEnterYourComplain.name: "Пожалуйста, введите вашу жалобу!",
  EnumLocal.txtPleaseEnterYourContactNumber.name: "Пожалуйста, введите свой контактный номер !!",
  EnumLocal.txtPleaseUploadScreenShort.name: "Пожалуйста, загрузите короткий скрин!!",
  EnumLocal.txtYourComplainSendSuccessfully.name: "Ваша жалоба успешно отправлена",
  EnumLocal.txtCoinRechargeSuccess.name: "Успех пополнения монеты",
  EnumLocal.txtPleaseUploadProfileImage.name: "Пожалуйста, загрузите изображение профиля!",
  EnumLocal.txtPleaseUploadDocumentImage.name: "Пожалуйста, загрузите изображение документа!",
  EnumLocal.txtPleaseEnterYourIdOnNumber.name: "Пожалуйста, введите свой идентификатор по номеру!",
  EnumLocal.txtPleaseEnterYourIdOnName.name: "Пожалуйста, введите свой идентификатор по имени !!",
  EnumLocal.txtPleaseEnterYourIdOnAddress.name: "Пожалуйста, введите свой идентификатор по адресу!",
  EnumLocal.txtVerificationRequestSendSuccessfully.name: "Запрос на подтверждение отправлен успешно",
  EnumLocal.txtLongPressToEnableAudioRecording.name: "Длительное нажатие, чтобы включить запись звука",
  EnumLocal.txtAudioRecording.name: "Аудио запись...",
  EnumLocal.txtOptionalInBrackets.name: "",
  EnumLocal.txtTheUserDoesNotHaveSufficientFundsToMakeTheWithdrawal.name: "",
  EnumLocal.txtVideo.name: "(Необязательный)",
  EnumLocal.txtPost.name: "У пользователя недостаточно средств для вывода средств",
  EnumLocal.txtHastTag.name: "видео",
  EnumLocal.txtCaption.name: "Почта",
  EnumLocal.txtReceivedCoin.name: "ХастТег",
  EnumLocal.txtSendCoin.name: "Подпись",
  EnumLocal.txtAddWallet.name: "Получена монета",
  EnumLocal.txtWithdrawal.name: "Отправить монету",
  EnumLocal.txtPendingWithdrawal.name: "Добавить кошелек",
  EnumLocal.txtCancelWithdrawal.name: "Снятие",
  EnumLocal.txtTypeYourHashtag.name: "Введите свой хэштег...",
  EnumLocal.txtYouCantSendGiftOwnVideo.name: "Вы не можете отправить подарок к собственному видео.",
  EnumLocal.txtWelcomeBonusCoin.name: "Приветственная бонусная монета",
  EnumLocal.txtSendGiftCoin.name: "Отправить подарочную монету",
  EnumLocal.txtReceiveGiftCoin.name: "Получить подарочную монету",
  EnumLocal.txtYouDonHaveSufficientCoinsToSendTheGift.name: "У вас недостаточно монет для отправки подарка!",
  EnumLocal.txtEnterYourTextWithHashtag.name: "Введите текст с хештегом...",
  EnumLocal.txtDeleteAll.name: "Удалить все",
  EnumLocal.txtRequests.name: "Запрос",
  EnumLocal.txtDecline.name: "Отклонить",
  EnumLocal.txtAccept.name: "Принимать",
  EnumLocal.txtIgnore.name: "игнорировать",
  EnumLocal.txtAcceptMessageRequestFrom.name: "Принять запрос на сообщение от",
  EnumLocal.txtUploadYourFirstVideo.name: "Принять запрос на сообщение от",
  EnumLocal.txtEnterVerificationCode.name: "Загрузите свое первое видео",
  EnumLocal.txtEnterVerificationCodeAsUnder.name: "Введите\nкод подтверждения",
  EnumLocal.txtSendCodeThisNumber.name: "Введите код подтверждения, как указано ниже.",
  EnumLocal.txtResendCode.name: "отправь код на этот номер",
  EnumLocal.EnterMobileNumber.name: "Повторно отправить код",
  EnumLocal.txtEnterYourMobileNumberHereAndContinue.name: "Введите\nномер мобильного телефона",
  EnumLocal.txtVerify.name: "Введите здесь свой номер мобильного телефона и продолжайте...",
  EnumLocal.txtMobileNumHintTest.name: "Проверять",
  EnumLocal.txtPleaseEnterCorrectMobileNumber.name: "Введите номер мобильного телефона",
  EnumLocal.txtPleaseEnterMobileNumber.name: "Пожалуйста, введите правильный номер мобильного телефона",
  EnumLocal.txtVerificationTimeout.name: "Пожалуйста, введите номер мобильного телефона",
  EnumLocal.txtVerificationCodeSend.name: "Тайм-аут проверки",
  EnumLocal.txtPleaseEnterVerificationCode.name: "Код подтверждения отправлен.",
  EnumLocal.txtMoreOption.name: "Пожалуйста, введите код подтверждения",
  EnumLocal.txtDeletePost.name: "Больше вариантов",
  EnumLocal.txtDeleteVideo.name: "Удалить сообщение",
  EnumLocal.txtDeletePostVideoContent.name: "Удалить видео",
  EnumLocal.txtMobile.name: "мобильный",
  EnumLocal.txtGoogle.name: "Google",
  EnumLocal.txtOr.name: "или",
  EnumLocal.txtUseAudio.name: "Использовать аудио",
  EnumLocal.txtAudio.name: "Аудио",
  EnumLocal.txtEditPost.name: "Редактировать сообщение",
  EnumLocal.txtEditReels.name: "Редактировать барабаны",
  EnumLocal.txtEdit.name: "Редактировать",
};
