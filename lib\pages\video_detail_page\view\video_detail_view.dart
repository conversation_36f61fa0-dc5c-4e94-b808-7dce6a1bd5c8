import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:preload_page_view/preload_page_view.dart';
import 'package:shortie/pages/video_detail_page/controller/video_detail_controller.dart';
import 'package:shortie/pages/video_detail_page/widget/video_detail_widget.dart';
import 'package:shortie/shimmer/reels_shimmer_ui.dart';
import 'package:shortie/ui/no_data_found_ui.dart';
import 'package:shortie/utils/color.dart';
import 'package:shortie/utils/enums.dart';

class VideoDetailView extends StatelessWidget {
  const VideoDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.black,
      appBar: AppBar(
        backgroundColor: AppColor.black,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColor.white),
          onPressed: () => Get.back(),
        ),
        title: Text(
          EnumLocal.txtVideo.name.tr,
          style: const TextStyle(color: AppColor.white),
        ),
        elevation: 0,
      ),
      body: GetBuilder<VideoDetailController>(
        id: "onGetVideoDetails",
        builder: (controller) {
          if (controller.isLoading) {
            return const ReelsShimmerUi();
          }
          
          if (controller.videoList.isEmpty) {
            return const NoDataFoundUi(
              iconSize: 140,
              fontSize: 16,
            );
          }

          return PreloadPageView.builder(
            controller: PreloadPageController(
              initialPage: controller.currentVideoIndex,
            ),
            itemCount: controller.videoList.length,
            preloadPagesCount: 3,
            scrollDirection: Axis.vertical,
            onPageChanged: (index) {
              controller.onVideoChanged(index);
            },
            itemBuilder: (context, index) {
              return GetBuilder<VideoDetailController>(
                id: "onVideoChanged",
                builder: (controller) => VideoDetailWidget(
                  index: index,
                  currentPageIndex: controller.currentVideoIndex,
                  videoData: controller.videoList[index],
                ),
              );
            },
          );
        },
      ),
    );
  }
}
