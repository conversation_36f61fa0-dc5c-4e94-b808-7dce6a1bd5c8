import 'dart:io';
import 'package:blurrycontainer/blurrycontainer.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:readmore/readmore.dart';
import 'package:shortie/custom/custom_format_number.dart';
import 'package:shortie/custom/custom_share.dart';
import 'package:shortie/pages/bottom_bar_page/controller/bottom_bar_controller.dart';
import 'package:shortie/pages/reels_page/model/fetch_reels_model.dart';
import 'package:shortie/routes/app_routes.dart';
import 'package:shortie/ui/loading_ui.dart';
import 'package:shortie/ui/preview_network_image_ui.dart';
import 'package:shortie/ui/preview_profile_bottom_sheet_ui.dart';
import 'package:shortie/custom/custom_icon_button.dart';
import 'package:shortie/ui/comment_bottom_sheet_ui.dart';
import 'package:shortie/main.dart';
import 'package:shortie/pages/reels_page/api/reels_like_dislike_api.dart';
import 'package:shortie/pages/reels_page/api/reels_share_api.dart';
import 'package:shortie/ui/report_bottom_sheet_ui.dart';
import 'package:shortie/ui/send_gift_on_video_bottom_sheet_ui.dart';
import 'package:shortie/ui/video_picker_bottom_sheet_ui.dart';
import 'package:shortie/utils/api.dart';
import 'package:shortie/utils/asset.dart';
import 'package:shortie/utils/branch_io_services.dart';
import 'package:shortie/utils/color.dart';
import 'package:shortie/utils/constant.dart';
import 'package:shortie/utils/database.dart';
import 'package:shortie/utils/enums.dart';
import 'package:shortie/utils/font_style.dart';
import 'package:shortie/utils/utils.dart';
import 'package:vibration/vibration.dart';
import 'package:video_player/video_player.dart';

class VideoDetailWidget extends StatefulWidget {
  const VideoDetailWidget({
    super.key, 
    required this.index, 
    required this.currentPageIndex,
    required this.videoData,
  });

  final int index;
  final int currentPageIndex;
  final Data videoData;

  @override
  State<VideoDetailWidget> createState() => _VideoDetailWidgetState();
}

class _VideoDetailWidgetState extends State<VideoDetailWidget> with SingleTickerProviderStateMixin {
  // Use unique keys for each widget instance to prevent GlobalKey conflicts
  late final String _widgetId;
  
  ChewieController? chewieController;
  VideoPlayerController? videoPlayerController;

  RxBool isPlaying = true.obs;
  RxBool isShowIcon = false.obs;
  RxBool isBuffering = false.obs;
  RxBool isVideoLoading = true.obs;
  RxBool isShowLikeAnimation = false.obs;
  RxBool isShowLikeIconAnimation = false.obs;
  RxBool isReelsPage = true.obs;
  RxBool isLike = false.obs;
  RxMap customChanges = {"like": 0, "comment": 0}.obs;
  RxBool isReadMore = false.obs;

  AnimationController? _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    
    // Create unique widget ID to prevent conflicts
    _widgetId = 'video_detail_${widget.index}_${DateTime.now().millisecondsSinceEpoch}';
    
    _controller = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    )..repeat();

    if (_controller != null) {
      _animation = Tween(begin: 0.0, end: 1.0).animate(_controller!);
    }
    
    initializeVideoPlayer();
    customSetting();
  }

  @override
  void dispose() {
    _controller?.dispose();
    onDisposeVideoPlayer();
    Utils.showLog("VideoDetailWidget disposed: $_widgetId");
    super.dispose();
  }

  Future<void> initializeVideoPlayer() async {
    try {
      String videoPath = widget.videoData.videoUrl ?? "";
      
      if (videoPath.isEmpty) {
        Utils.showLog("Video URL is empty for video: ${widget.videoData.id}");
        return;
      }

      videoPlayerController = VideoPlayerController.networkUrl(Uri.parse(Api.baseUrl + videoPath));

      await videoPlayerController?.initialize();

      if (videoPlayerController != null && (videoPlayerController?.value.isInitialized ?? false)) {
        chewieController = ChewieController(
          videoPlayerController: videoPlayerController!,
          looping: true,
          allowedScreenSleep: false,
          allowMuting: false,
          showControlsOnInitialize: false,
          showControls: false,
          maxScale: 1,
        );

        if (chewieController != null) {
          isVideoLoading.value = false;
          (widget.index == widget.currentPageIndex && isReelsPage.value) ? onPlayVideo() : null;
        } else {
          isVideoLoading.value = true;
        }

        videoPlayerController?.addListener(() {
          if (mounted) {
            (videoPlayerController?.value.isBuffering ?? false) ? isBuffering.value = true : isBuffering.value = false;

            if (isReelsPage.value == false) {
              onStopVideo();
            }
          }
        });
      }
    } catch (e) {
      onDisposeVideoPlayer();
      Utils.showLog("Video initialization failed for $_widgetId: $e");
    }
  }

  void onStopVideo() {
    if (mounted) {
      isPlaying.value = false;
      videoPlayerController?.pause();
    }
  }

  void onPlayVideo() {
    if (mounted) {
      isPlaying.value = true;
      videoPlayerController?.play();
    }
  }

  void onDisposeVideoPlayer() {
    try {
      onStopVideo();
      videoPlayerController?.dispose();
      chewieController?.dispose();
      chewieController = null;
      videoPlayerController = null;
      if (mounted) {
        isVideoLoading.value = true;
      }
    } catch (e) {
      Utils.showLog("Error disposing video player for $_widgetId: $e");
    }
  }

  void customSetting() {
    isLike.value = widget.videoData.isLike ?? false;
    customChanges["like"] = int.parse(widget.videoData.totalLikes?.toString() ?? "0");
    customChanges["comment"] = int.parse(widget.videoData.totalComments?.toString() ?? "0");
  }

  void onClickVideo() async {
    if (isVideoLoading.value == false && mounted) {
      videoPlayerController!.value.isPlaying ? onStopVideo() : onPlayVideo();
      isShowIcon.value = true;
      await 2.seconds.delay();
      if (mounted) {
        isShowIcon.value = false;
      }
    }
    if (isReelsPage.value == false) {
      isReelsPage.value = true;
    }
  }

  void onClickPlayPause() async {
    if (mounted) {
      videoPlayerController!.value.isPlaying ? onStopVideo() : onPlayVideo();
      if (isReelsPage.value == false) {
        isReelsPage.value = true;
      }
    }
  }

  Future<void> onClickShare() async {
    if (!mounted) return;
    
    isReelsPage.value = false;

    Get.dialog(const LoadingUi(), barrierDismissible: false);

    await BranchIoServices.onCreateBranchIoLink(
      id: widget.videoData.id ?? "",
      name: widget.videoData.caption ?? "",
      image: widget.videoData.videoImage ?? "",
      userId: widget.videoData.userId ?? "",
      pageRoutes: "Video",
    );

    final link = await BranchIoServices.onGenerateLink();

    Get.back();

    if (link != null) {
      CustomShare.onShareLink(link: link);
    }
    await ReelsShareApi.callApi(loginUserId: Database.loginUserId, videoId: widget.videoData.id!);
  }

  Future<void> onClickLike() async {
    if (!mounted) return;
    
    if (isLike.value) {
      isLike.value = false;
      customChanges["like"]--;
    } else {
      isLike.value = true;
      customChanges["like"]++;
    }

    isShowLikeIconAnimation.value = true;
    await 500.milliseconds.delay();
    if (mounted) {
      isShowLikeIconAnimation.value = false;
    }

    await ReelsLikeDislikeApi.callApi(
      loginUserId: Database.loginUserId,
      videoId: widget.videoData.id!,
    );
  }

  Future<void> onDoubleClick() async {
    if (!mounted) return;
    
    if (isLike.value) {
      isLike.value = false;
      customChanges["like"]--;
    } else {
      isLike.value = true;
      customChanges["like"]++;

      isShowLikeAnimation.value = true;
      Vibration.vibrate(duration: 50, amplitude: 128);
      await 1200.milliseconds.delay();
      if (mounted) {
        isShowLikeAnimation.value = false;
      }
    }
    await ReelsLikeDislikeApi.callApi(
      loginUserId: Database.loginUserId,
      videoId: widget.videoData.id!,
    );
  }

  Future<void> onClickComment() async {
    if (!mounted) return;
    
    isReelsPage.value = false;
    customChanges["comment"] = await CommentBottomSheetUi.show(
      context: context,
      commentType: 2,
      commentTypeId: widget.videoData.id!,
      totalComments: customChanges["comment"],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.index == widget.currentPageIndex) {
      isReadMore.value = false;
      (isVideoLoading.value == false && isReelsPage.value) ? onPlayVideo() : null;
    } else {
      isVideoLoading.value == false ? videoPlayerController?.seekTo(Duration.zero) : null;
      onStopVideo();
    }
    
    return Scaffold(
      body: SizedBox(
        height: Get.height,
        width: Get.width,
        child: Stack(
          children: [
            GestureDetector(
              onTap: onClickVideo,
              onDoubleTap: onDoubleClick,
              child: Container(
                color: AppColor.black,
                height: (Get.height - AppConstant.bottomBarSize),
                width: Get.width,
                child: Obx(
                  () => isVideoLoading.value
                      ? Align(alignment: Alignment.bottomCenter, child: LinearProgressIndicator(color: AppColor.primary))
                      : SizedBox.expand(
                          child: FittedBox(
                            fit: BoxFit.cover,
                            child: SizedBox(
                              width: videoPlayerController?.value.size.width ?? 0,
                              height: videoPlayerController?.value.size.height ?? 0,
                              child: chewieController != null ? Chewie(controller: chewieController!) : Container(),
                            ),
                          ),
                        ),
                ),
              ),
            ),
            // Add the rest of the UI components here...
            _buildVideoControls(),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoControls() {
    return Stack(
      children: [
        // Watermark
        Positioned(
          top: MediaQuery.of(context).viewPadding.top + 15,
          left: 20,
          child: Visibility(
            visible: Utils.isShowWaterMark,
            child: CachedNetworkImage(
              imageUrl: Utils.waterMarkIcon,
              fit: BoxFit.contain,
              imageBuilder: (context, imageProvider) => Image(
                image: ResizeImage(imageProvider, width: Utils.waterMarkSize, height: Utils.waterMarkSize),
                fit: BoxFit.contain,
              ),
              placeholder: (context, url) => const Offstage(),
              errorWidget: (context, url, error) => const Offstage(),
            ),
          ),
        ),
        
        // Gift animation center
        Align(
          alignment: Alignment.center,
          child: SendGiftOnVideoBottomSheetUi.onShowGift(),
        ),
        
        // Like animation
        Obx(
          () => Visibility(
            visible: isShowLikeAnimation.value,
            child: Align(
              alignment: Alignment.center, 
              child: Lottie.asset(AppAsset.lottieLike, fit: BoxFit.cover, height: 300, width: 300)
            ),
          ),
        ),
        
        // Play/Pause button
        Obx(
          () => isShowIcon.value
              ? Align(
                  alignment: Alignment.center,
                  child: GestureDetector(
                    onTap: onClickPlayPause,
                    child: Container(
                      height: 70,
                      width: 70,
                      padding: EdgeInsets.only(left: isPlaying.value ? 0 : 2),
                      decoration: BoxDecoration(color: AppColor.black.withOpacity(0.2), shape: BoxShape.circle),
                      child: Center(
                        child: Image.asset(
                          isPlaying.value ? AppAsset.icPause : AppAsset.icPlay,
                          width: 30,
                          height: 30,
                          color: AppColor.white,
                        ),
                      ),
                    ),
                  ),
                )
              : const Offstage(),
        ),
        
        // Right side controls
        _buildRightSideControls(),
      ],
    );
  }

  Widget _buildRightSideControls() {
    return Positioned(
      right: 0,
      child: Container(
        padding: const EdgeInsets.only(top: 30, bottom: 100),
        height: Get.height,
        child: Column(
          children: [
            const Spacer(),
            // Like button
            Obx(
              () => SizedBox(
                height: 40,
                child: AnimatedContainer(
                  duration: Duration(milliseconds: 300),
                  height: isShowLikeIconAnimation.value ? 15 : 50,
                  width: isShowLikeIconAnimation.value ? 15 : 50,
                  alignment: Alignment.center,
                  child: CustomIconButton(
                    icon: AppAsset.icLike,
                    callback: onClickLike,
                    iconSize: 34,
                    iconColor: isLike.value ? AppColor.colorRedContainer : AppColor.white,
                  ),
                ),
              ),
            ),
            Obx(
              () => Text(
                CustomFormatNumber.convert(customChanges["like"]),
                style: AppFontStyle.styleW700(AppColor.white, 14),
              ),
            ),
            15.height,
            // Comment button
            CustomIconButton(icon: AppAsset.icComment, circleSize: 40, callback: onClickComment, iconSize: 34),
            Obx(
              () => Text(
                CustomFormatNumber.convert(customChanges["comment"]),
                style: AppFontStyle.styleW700(AppColor.white, 14),
              ),
            ),
            15.height,
            // Share button
            CustomIconButton(
              circleSize: 40,
              icon: AppAsset.icShare,
              callback: onClickShare,
              iconSize: 32,
              iconColor: AppColor.white,
            ),
          ],
        ),
      ),
    );
  }
}
