<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>ball00.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{2,2}</string>
                <key>spriteSourceSize</key>
                <string>{2,2}</string>
                <key>textureRect</key>
                <string>{{1,1},{2,2}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>ball01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{2,2}</string>
                <key>spriteSourceSize</key>
                <string>{2,2}</string>
                <key>textureRect</key>
                <string>{{1,1},{2,2}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>ball02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{2,2}</string>
                <key>spriteSourceSize</key>
                <string>{2,2}</string>
                <key>textureRect</key>
                <string>{{1,1},{2,2}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>ball.pkm</string>
            <key>size</key>
            <string>{4,4}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:556e4ebe701b6900e8a184ffc89d21df:73ca054b5e3a84e875754db218cd4292:e89ed3222c3766ec13f1325ab23e67a6$</string>
            <key>textureFileName</key>
            <string>ball.pkm</string>
        </dict>
    </dict>
</plist>
