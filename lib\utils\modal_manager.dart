import 'package:flutter/material.dart';

/// Global modal manager to prevent multiple modal instances and GlobalKey conflicts
class ModalManager {
  static final ModalManager _instance = ModalManager._internal();
  factory ModalManager() => _instance;
  ModalManager._internal();

  // Track open modals to prevent duplicates
  final Set<String> _openModals = <String>{};
  
  /// Check if a modal is already open
  bool isModalOpen(String modalId) {
    return _openModals.contains(modalId);
  }
  
  /// Register a modal as open
  void registerModal(String modalId) {
    _openModals.add(modalId);
  }
  
  /// Unregister a modal when closed
  void unregisterModal(String modalId) {
    _openModals.remove(modalId);
  }
  
  /// Safe showModalBottomSheet wrapper
  Future<T?> showSafeModalBottomSheet<T>({
    required BuildContext context,
    required WidgetBuilder builder,
    required String modalId,
    bool isScrollControlled = false,
    bool isDismissible = true,
    bool enableDrag = true,
    Color? backgroundColor,
    double? elevation,
    ShapeBorder? shape,
    Clip? clipBehavior,
    BoxConstraints? constraints,
    Color? barrierColor,
    bool useRootNavigator = true,
    RouteSettings? routeSettings,
    AnimationController? transitionAnimationController,
    Offset? anchorPoint,
  }) async {
    // Prevent duplicate modals
    if (isModalOpen(modalId)) {
      return null;
    }
    
    registerModal(modalId);
    
    try {
      final result = await showModalBottomSheet<T>(
        context: context,
        builder: builder,
        isScrollControlled: isScrollControlled,
        isDismissible: isDismissible,
        enableDrag: enableDrag,
        backgroundColor: backgroundColor,
        elevation: elevation,
        shape: shape,
        clipBehavior: clipBehavior,
        constraints: constraints,
        barrierColor: barrierColor,
        useRootNavigator: useRootNavigator,
        routeSettings: routeSettings,
        transitionAnimationController: transitionAnimationController,
        anchorPoint: anchorPoint,
      );
      
      return result;
    } finally {
      unregisterModal(modalId);
    }
  }
  
  /// Safe showDialog wrapper
  Future<T?> showSafeDialog<T>({
    required BuildContext context,
    required WidgetBuilder builder,
    required String dialogId,
    bool barrierDismissible = true,
    Color? barrierColor,
    String? barrierLabel,
    bool useRootNavigator = true,
    RouteSettings? routeSettings,
    Offset? anchorPoint,
    TraversalEdgeBehavior? traversalEdgeBehavior,
  }) async {
    // Prevent duplicate dialogs
    if (isModalOpen(dialogId)) {
      return null;
    }
    
    registerModal(dialogId);
    
    try {
      final result = await showDialog<T>(
        context: context,
        builder: builder,
        barrierDismissible: barrierDismissible,
        barrierColor: barrierColor,
        barrierLabel: barrierLabel,
        useRootNavigator: useRootNavigator,
        routeSettings: routeSettings,
        anchorPoint: anchorPoint,
        traversalEdgeBehavior: traversalEdgeBehavior,
      );
      
      return result;
    } finally {
      unregisterModal(dialogId);
    }
  }
  
  /// Clear all registered modals (use with caution)
  void clearAll() {
    _openModals.clear();
  }
}
