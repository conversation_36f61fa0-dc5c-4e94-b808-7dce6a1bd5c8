<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
<!--    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION"/>-->
<!--    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/>-->
<!--    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO"/>-->
<!--    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO"/>-->
    <uses-permission android:name="Manifest.permission.CAPTURE_AUDIO_OUTPUT"  />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.VIBRATE"/>
    <uses-permission android:name="com.android.vending.BILLING" />

    <!-- Live Streaming Permission -->

    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
<!--    <uses-permission android:name="android.permission.READ_PHONE_STATE" />-->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-feature android:glEsVersion="0x00020000" android:required="true" />
    <uses-feature android:name="android.hardware.camera" />
    <uses-feature android:name="android.hardware.camera.autofocus" />


    <application
        android:label="Ratulive"
        android:name="${applicationName}"
        android:icon="@mipmap/launcher_icon"
        android:usesCleartextTraffic="true"
        android:requestLegacyExternalStorage="true">

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme"
                />

            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
            <intent-filter>
                <action android:name="FLUTTER_NOTIFICATION_CLICK" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <!-- Branch URI Scheme -->
            <intent-filter>
                <!-- If utilizing $deeplink_path please explicitly declare your hosts, or utilize a wildcard(*) -->
                <!-- REPLACE `android:scheme` with your Android URI scheme -->
                <data android:scheme="your custom scheme here" android:host="open" />
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>

            <!--            &lt;!&ndash; Branch App Links - Live App &ndash;&gt;-->
            <!--            <intent-filter android:autoVerify="true">-->
            <!--                <action android:name="android.intent.action.VIEW" />-->
            <!--                <category android:name="android.intent.category.DEFAULT" />-->
            <!--                <category android:name="android.intent.category.BROWSABLE" />-->
            <!--                &lt;!&ndash; REPLACE `android:host` with your `app.link` domain &ndash;&gt;-->
            <!--                <data android:scheme="https" android:host="example.app.link" />-->
            <!--                &lt;!&ndash; REPLACE `android:host` with your `-alternate` domain (required for proper functioning of App Links and Deepviews) &ndash;&gt;-->
            <!--                <data android:scheme="https" android:host="example-alternate.app.link" />-->
            <!--            </intent-filter>-->

            <!-- Branch App Links - Test App -->
<!--            <intent-filter android:autoVerify="true">-->
<!--                <action android:name="android.intent.action.VIEW" />-->
<!--                <category android:name="android.intent.category.DEFAULT" />-->
<!--                <category android:name="android.intent.category.BROWSABLE" />-->
<!--                <data android:scheme="https" android:host="Enter_your_test_app_link" />-->
                <!-- REPLACE `android:host` with your `-alternate` domain (required for proper functioning of App Links and Deepviews) -->
<!--                <data android:scheme="https" android:host="Enter_your_test_app_link-alternate" />-->
<!--            </intent-filter>-->



            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- &lt;!&ndash; REPLACE `android:host` with your `app.link` domain &ndash;&gt; -->
                <data android:scheme="https" android:host="ratulive.app.link" />
                <!-- &lt;!&ndash; REPLACE `android:host` with your `-alternate` domain (required for proper functioning of App Links and Deepviews) &ndash;&gt; -->
                <data android:scheme="https" android:host="ratulive-alternate.app.link" />
            </intent-filter>



        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
        <!-- *** >>> Google Ad Code <<< *** <<< Code Start -->
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-3940256099942544~3347511713"/>
        <!-- *** >>> Google Ad Code <<< *** Code End >>> -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@mipmap/shortie_notification_icon" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@android:color/transparent" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="high_importance_channel" />
        <!-- Don't delete the meta-data below.
            This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data android:name="io.branch.sdk.BranchKey" android:value="key_live_avwixet0Lk8mwMkgObX83jofxrgINDPe" />
        <!-- REPLACE `BranchKey.test` with the value from your Branch Dashboard -->
        <meta-data android:name="io.branch.sdk.BranchKey.test" android:value="key_test_kBshEfr4Jf8cBVpgQh743hhhrydLRFt0" />
        <!-- Set to `true` to use `BranchKey.test` -->
        <meta-data android:name="io.branch.sdk.TestMode" android:value="false" />
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
</manifest>


