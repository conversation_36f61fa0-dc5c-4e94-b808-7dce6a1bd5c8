import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:shortie/main.dart';

void main() {
  group('GlobalKey Fix Tests', () {
    testWidgets('GetMaterialApp should not have duplicate GlobalKeys', (WidgetTester tester) async {
      // Test that our fix prevents GlobalKey duplication
      await tester.pumpWidget(const MyApp());
      
      // The app should build without GlobalKey errors
      expect(find.byType(GetMaterialApp), findsOneWidget);
      
      // Verify no duplicate navigator keys
      final navigators = find.byType(Navigator);
      expect(navigators, findsWidgets);
      
      // Test should pass without GlobalKey exceptions
    });

    testWidgets('Video link navigation should work without GlobalKey errors', (WidgetTester tester) async {
      // Simulate the video link scenario
      await tester.pumpWidget(const MyApp());
      
      // Wait for app to initialize
      await tester.pumpAndSettle();
      
      // The app should be stable without GlobalKey conflicts
      expect(tester.takeException(), isNull);
    });

    test('GetX navigation should not conflict with manual navigator keys', () {
      // This test verifies that removing the manual navigatorKey
      // prevents conflicts with GetX's internal navigation system
      
      // GetX should handle navigation internally without external keys
      expect(Get.key, isNotNull);
      
      // No manual navigator key should be set
      // This prevents the GlobalKey duplication error
    });
  });
}
