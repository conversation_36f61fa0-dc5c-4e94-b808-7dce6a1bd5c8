import 'package:shortie/utils/enums.dart';

final Map<String, String> enUS = {
  EnumLocal.txtAppName.name: "<PERSON><PERSON>",
  EnumLocal.txtSkip.name: "<PERSON><PERSON>",
  EnumLocal.txtOnBoardingTitle_1.name: "The Best Social Media App Of The Century",
  EnumLocal.txtOnBoardingTitle_2.name: "Let’s Connect With Everyone In The World",
  EnumLocal.txtOnBoardingTitle_3.name: "Everything You Can Do In The App",
  EnumLocal.txtOnBoardingSubTitle_1.name: "Experience the future of social media with our cutting-edge features and intuitive design. Get ready to connect, share, and discover like never before!",
  EnumLocal.txtOnBoardingSubTitle_2.name: "Join a global community that's passionate, diverse, and vibrant. Connect with others, share your story, and make the world a smaller, more wonderful place!",
  EnumLocal.txtOnBoardingSubTitle_3.name: "From sharing life's moments to discovering new interests, our app has got you covered. Explore, create, and thrive in a world of endless possibilities!",
  EnumLocal.txtLoginTitle.name: "Let’s meet new people around you",
  EnumLocal.txtLoginSubTitle.name: "Welcome! Sign in quickly or use your Google account to start meeting new people around you. Let's begin your journey to new friendships!",
  EnumLocal.txtQuickLogIn.name: "Quick Log In",
  EnumLocal.txtContinueWithGoogle.name: "Continue with Google",
  EnumLocal.txtFillProfile.name: "Fill Profile",
  EnumLocal.txtEditProfile.name: "Edit Profile",
  EnumLocal.txtFullName.name: "Full Name",
  EnumLocal.txtUserName.name: "User Name",
  EnumLocal.txtIdentificationCode.name: "Identification Code",
  EnumLocal.txtCountry.name: "Country",
  EnumLocal.txtBioDetails.name: "Bio Details",
  EnumLocal.txtGender.name: "Gender",
  EnumLocal.txtMale.name: "Male",
  EnumLocal.txtFemale.name: "Female",
  EnumLocal.txtOther.name: "Other",
  EnumLocal.txtSaveProfile.name: "Save Profile",
  EnumLocal.txtSendGift.name: "Send Gift",
  EnumLocal.txtSend.name: "Send",
  EnumLocal.txtCoins.name: "Coins",
  EnumLocal.txtRecharge.name: "Recharge",
  EnumLocal.txtComment.name: "Comment",
  EnumLocal.txtTypeComment.name: "Type Comment...",
  EnumLocal.txtVideos.name: "Videos",
  EnumLocal.txtNext.name: "Next",
  EnumLocal.txtImages.name: "Images",
  EnumLocal.txtAddMusic.name: "Add Music",
  EnumLocal.txtSearchHintText.name: "Search...",
  EnumLocal.txtDiscover.name: "Discover",
  EnumLocal.txtFavourite.name: "Favourite",
  EnumLocal.txtPreview.name: "Preview",
  EnumLocal.txtUploadReels.name: "Upload Reels",
  EnumLocal.txtWhatsOnYourMind.name: "What’s on your mind?",
  EnumLocal.txtChangeThumbnail.name: "Change Thumbnail",
  EnumLocal.txtAddTopic.name: "Add topic",
  EnumLocal.txtUpload.name: "Upload",
  EnumLocal.txtAddHashtag.name: "Add Hashtag",
  EnumLocal.txtDone.name: "Done",
  EnumLocal.txtLiveStreaming.name: "Live Streaming",
  EnumLocal.txtGoLive.name: "Go Live",
  EnumLocal.txtLive.name: "Live",
  EnumLocal.txtStopLive.name: "Stop Live",
  EnumLocal.txtStop.name: "Stop",
  EnumLocal.txtStopLiveDialogText.name: "Stop live stream? This action cannot be undone and will terminate the broadcast immediately. Click 'Stop Streaming' to confirm.",
  EnumLocal.txtFeeds.name: "Feeds",
  EnumLocal.txtCreate.name: "Create",
  EnumLocal.txtSayHi.name: "Say “Hi”",
  EnumLocal.txtReport.name: "Report",
  EnumLocal.txtCancel.name: "Cancel",
  EnumLocal.txtItIsSpam.name: "It is spam",
  EnumLocal.txtNudityOrSexualActivity.name: "Nudity or sexual activity",
  EnumLocal.txtHateSpeechOrSymbols.name: "Hate speech or symbols",
  EnumLocal.txtViolenceOrDangerousOrganization.name: "Violence or dangerous organization",
  EnumLocal.txtFalseInformation.name: "False information",
  EnumLocal.txtBullyingOrHarassment.name: "Bullying or harassment",
  EnumLocal.txtScamOrFraud.name: "Scam or fraud",
  EnumLocal.txtIntellectualPropertyViolation.name: "Intellectual property violation",
  EnumLocal.txtSuicideOrSelfInjury.name: "Suicide or self injury",
  EnumLocal.txtDrugs.name: "Drugs",
  EnumLocal.txtEatingDisorders.name: "Eating disorders",
  EnumLocal.txtSomethingElse.name: "Something else",
  EnumLocal.txtChildAbuse.name: "Child Abuse",
  EnumLocal.txtOthers.name: "Others",
  EnumLocal.txtUploadPost.name: "Upload Post",
  EnumLocal.txtSearch.name: "Search",
  EnumLocal.txtHashTag.name: "Hash Tag",
  EnumLocal.txtViewAll.name: "View all",
  EnumLocal.txtScanQRCode.name: "Scan QR Code",
  EnumLocal.txtScanQRCodeText.name: "Scan QR code to see profile ",
  EnumLocal.txtMyQRCodeText.name: "Welcome to my QR code page! By scanning the QR code Above, you'll instantly access my profile data. This includes my contact information, social media profiles, and other essential details that will help you connect with me effortlessly. Simply point your smartphone camera at the QR code, and you'll be directed to my personalized profile page. Stay connected and learn more about me with just a quick scan!",
  EnumLocal.txtViewProfile.name: "View Profile",
  EnumLocal.txtViewDetails.name: "View Details",
  EnumLocal.txtFollow.name: "Follow",
  EnumLocal.txtFollowing.name: "Following",
  EnumLocal.txtLikes.name: "Likes",
  EnumLocal.txtFollowers.name: "Followers",
  EnumLocal.txtReels.name: "Reels",
  EnumLocal.txtCollections.name: "Collections",
  EnumLocal.txtSettings.name: "Settings",
  EnumLocal.txtAccount.name: "Account",
  EnumLocal.txtNotifyMe.name: "Notify Me",
  EnumLocal.txtLanguages.name: "Languages",
  EnumLocal.txtMyWallet.name: "My Wallet",
  EnumLocal.txtShareProfile.name: "Share Profile",
  EnumLocal.txtMyQRCode.name: "My QR Code",
  EnumLocal.txtVerificationRequest.name: "Verification Request",
  EnumLocal.txtGeneral.name: "General",
  EnumLocal.txtHelp.name: "Help",
  EnumLocal.txtTermsOfUse.name: "Terms of Use",
  EnumLocal.txtPrivacyPolicy.name: "Privacy Policy",
  EnumLocal.txtLogOut.name: "Log Out",
  EnumLocal.txtDeleteAccount.name: "Delete Account",
  EnumLocal.txtDelete.name: "Delete",
  EnumLocal.txtLogOutText.name: "You are about to log out of your account. You will be redirected to the login page. Are you sure you want to continue?",
  EnumLocal.txtDeleteAccountText.name: "Are you sure you want to permanently delete your account? This action cannot be undone and will result in the loss of all your data and access to associated services.",
  EnumLocal.txtUploadYourImages.name: "Upload Your Images",
  EnumLocal.txtUploadIDPhotos.name: "Upload ID Photos",
  EnumLocal.txtPersonalPhotos.name: "Personal Photos",
  EnumLocal.txtClearPhotos.name: "Clear Photos",
  EnumLocal.txtCapture.name: "Capture",
  EnumLocal.txtAttach.name: "Attach",
  EnumLocal.txtIDNumber.name: "ID Number",
  EnumLocal.txtNameOnID.name: "Name On ID",
  EnumLocal.txtFullAddress.name: "Full Address",
  EnumLocal.txtSubmit.name: "Submit",
  EnumLocal.txtAvailableCoinBalance.name: "Available Coin Balance",
  EnumLocal.txtRechargeCoin.name: "Recharge Coin",
  EnumLocal.txtCoinHistory.name: "Coin History",
  EnumLocal.txtWithdraw.name: "Withdraw",
  EnumLocal.txtAvailableCoin.name: "Available Coin",
  EnumLocal.txtEnterCoin.name: "Enter Coin",
  EnumLocal.txtEnterWithdrawCoin.name: "Enter withdraw coin...",
  EnumLocal.txtMinimumWithdraw.name: "*Minimum Withdraw",
  EnumLocal.txtSelectPaymentGateway.name: "Select Payment Gateway",
  EnumLocal.txtSelectPaymentMethod.name: "Select Payment Method",
  EnumLocal.txtPurchasePremiumPlanAndGetAllAccess.name: "Purchase Premium Plan & Get All Access",
  EnumLocal.txtRechargePageSubTitle.name: "Upgrade to the Premium Plan for unlimited access to advanced tools and exclusive resources. Elevate your experience now.",
  EnumLocal.txtConfirmWithdrawDialogText.name: "Please review the withdrawal details carefully to ensure accuracy. Double-check the destination account information and the withdrawal amount to avoid any errors.",
  EnumLocal.txtPurchasePlan.name: "Purchase Plan",
  EnumLocal.txtPayNow.name: "Pay Now",
  EnumLocal.txtMostPopularPlan.name: "Most Popular Plan",
  EnumLocal.txtComplaintOrSuggestion.name: "Complaint or Suggestion",
  EnumLocal.txtTypingSomethings.name: "Typing Somethings...",
  EnumLocal.txtContact.name: "Contact",
  EnumLocal.txtAttachYourImageOrScreenshot.name: "Attach Your Image or Screenshot",
  EnumLocal.txtBrowse.name: "Browse",
  EnumLocal.txtNone.name: "None",
  EnumLocal.txtChooseImage.name: "Choose Image",
  EnumLocal.txtGallery.name: "Gallery",
  EnumLocal.txtTakePhoto.name: "Take Photo",
  EnumLocal.txtChooseVideo.name: "Choose Video",
  EnumLocal.txtCreateReels.name: "Create Reels",
  EnumLocal.txtNoDataFound.name: "No data found !!",
  EnumLocal.txtTypeSomething.name: "Type Something...",
  EnumLocal.txtSomeThingWentWrong.name: "Some thing went wrong !!",
  EnumLocal.txtConnectionLost.name: "Connection Lost !!",
  EnumLocal.txtNoInternetConnection.name: "No Internet Connection !!",
  EnumLocal.txtLoginSuccess.name: "Login Success",
  EnumLocal.txtProfileUpdateSuccessfully.name: "Profile update successfully",
  EnumLocal.txtReelsUploadSuccessfully.name: "Reels upload successfully",
  EnumLocal.txtYouCantFollowYourOwnAccount.name: "You can't follow your own account",
  EnumLocal.txtReportSending.name: "Report sending...",
  EnumLocal.txtReportSendSuccess.name: "Report send success",
  EnumLocal.txtYouCanSelectMaximumFiveImages.name: "You can select maximum 5 images !!",
  EnumLocal.txtPostUploading.name: "Post Uploading...",
  EnumLocal.txtPostUploadSuccessfully.name: "Post upload successfully",
  EnumLocal.txtPleaseSelectProfileImage.name: "Please select profile image !!",
  EnumLocal.txtPleaseEnterFullName.name: "Please enter full name !!",
  EnumLocal.txtPleaseEnterUserName.name: "Please enter user name !!",
  EnumLocal.txtPleaseEnterBioDetails.name: "Please enter bio details !!",
  EnumLocal.txtPleaseAllowPermission.name: "Please allow permission !!",
  EnumLocal.txtPleaseWaitSomeTime.name: "Please wait sometime...",
  EnumLocal.txtPleaseEnterCaption.name: "Please enter caption !!",
  EnumLocal.txtPleaseSelectHashTag.name: "Please select hash tag !!",
  EnumLocal.txtPleaseSelectPost.name: "Please select post !!",
  EnumLocal.txtPleaseEnterWithdrawCoin.name: "Please enter withdraw coin !!",
  EnumLocal.txtPleaseSelectWithdrawMethod.name: "Please select withdraw method !!",
  EnumLocal.txtPleaseEnterAllPaymentDetails.name: "Please enter all payment details !!",
  EnumLocal.txtCoinNotAvailableForSendGiftText.name: "You don't have sufficient funds to send the gift !!",
  EnumLocal.txtWithdrawalRequestedCoinMustBeGreaterThanSpecifiedByTheAdmin.name: "Withdrawal requested coin must be greater than specified by the admin !!",
  EnumLocal.txtDownloadSuccess.name: "Download Success",
  EnumLocal.txtPleaseEnterYourComplain.name: "Please enter your complain !!",
  EnumLocal.txtPleaseEnterYourContactNumber.name: "Please enter your contact number !!",
  EnumLocal.txtPleaseUploadScreenShort.name: "Please upload screen short !!",
  EnumLocal.txtYourComplainSendSuccessfully.name: "Your complain send successfully",
  EnumLocal.txtCoinRechargeSuccess.name: "Coin recharge success",
  EnumLocal.txtPleaseUploadProfileImage.name: "Please upload profile image !!",
  EnumLocal.txtPleaseUploadDocumentImage.name: "Please upload document image !!",
  EnumLocal.txtPleaseEnterYourIdOnNumber.name: "Please enter your id on number !!",
  EnumLocal.txtPleaseEnterYourIdOnName.name: "Please enter your id on name !!",
  EnumLocal.txtPleaseEnterYourIdOnAddress.name: "Please enter your id on address !!",
  EnumLocal.txtVerificationRequestSendSuccessfully.name: "Verification request send successfully",
  EnumLocal.txtLongPressToEnableAudioRecording.name: "Long press to enable audio recording",
  EnumLocal.txtAudioRecording.name: "Audio Recording...",
  EnumLocal.txtOptionalInBrackets.name: "(Optional)",
  EnumLocal.txtTheUserDoesNotHaveSufficientFundsToMakeTheWithdrawal.name: "The user does not have sufficient funds to make the withdrawal",
  EnumLocal.txtVideo.name: "Video",
  EnumLocal.txtPost.name: "Post",
  EnumLocal.txtHastTag.name: "HastTag",
  EnumLocal.txtCaption.name: "Caption",
  EnumLocal.txtReceivedCoin.name: "Received Coin",
  EnumLocal.txtSendCoin.name: "Send Coin",
  EnumLocal.txtAddWallet.name: "Add Wallet",
  EnumLocal.txtWithdrawal.name: "Withdraw",
  EnumLocal.txtPendingWithdrawal.name: "Pending Withdraw",
  EnumLocal.txtCancelWithdrawal.name: "Cancel Withdraw",
  EnumLocal.txtTypeYourHashtag.name: "Type your hashtag...",
  EnumLocal.txtYouCantSendGiftOwnVideo.name: "You can't send gift to own video",
  EnumLocal.txtWelcomeBonusCoin.name: "Welcome Bonus Coin",
  EnumLocal.txtSendGiftCoin.name: "Send Gift Coin",
  EnumLocal.txtReceiveGiftCoin.name: "Receive Gift Coin",
  EnumLocal.txtYouDonHaveSufficientCoinsToSendTheGift.name: "You don't have sufficient coins to send the gift !!",
  EnumLocal.txtEnterYourTextWithHashtag.name: "Enter your text with hashtag...",
  EnumLocal.txtDeleteAll.name: "Delete All",
  EnumLocal.txtRequests.name: "Requests",
  EnumLocal.txtDecline.name: "Decline",
  EnumLocal.txtAccept.name: "Accept",
  EnumLocal.txtIgnore.name: "Ignore",
  EnumLocal.txtAcceptMessageRequestFrom.name: "Accept message request from",
  EnumLocal.txtUploadYourFirstVideo.name: "Upload your first video",
  EnumLocal.txtEnterVerificationCode.name: "Enter\nVerification Code",
  EnumLocal.txtEnterVerificationCodeAsUnder.name: "Enter verification code as under",
  EnumLocal.txtSendCodeThisNumber.name: "send code this number",
  EnumLocal.txtResendCode.name: "Resend Code",
  EnumLocal.EnterMobileNumber.name: "Enter\nMobile Number",
  EnumLocal.txtEnterYourMobileNumberHereAndContinue.name: "Enter your mobile number here and continue...",
  EnumLocal.txtVerify.name: "Verify",
  EnumLocal.txtMobileNumHintTest.name: "Enter mobile number",
  EnumLocal.txtPleaseEnterCorrectMobileNumber.name: "Please enter correct mobile number",
  EnumLocal.txtPleaseEnterMobileNumber.name: "Please Enter mobile number",
  EnumLocal.txtVerificationTimeout.name: "Verification timeout",
  EnumLocal.txtVerificationCodeSend.name: "Verification code send.",
  EnumLocal.txtPleaseEnterVerificationCode.name: "Please enter verification code",
  EnumLocal.txtMoreOption.name: "More Option",
  EnumLocal.txtDeletePost.name: "Delete Post",
  EnumLocal.txtDeleteVideo.name: "Delete Video",
  EnumLocal.txtDeletePostVideoContent.name: "Are you certain you want to proceed with deleting this item? Once deleted, this action cannot be undone, and the item will be permanently removed from your records. Please make sure you have reviewed your decision carefully before proceeding.",
  EnumLocal.txtMobile.name: "Mobile",
  EnumLocal.txtGoogle.name: "Google",
  EnumLocal.txtOr.name: "or",
  EnumLocal.txtUseAudio.name: "Use audio",
  EnumLocal.txtAudio.name: "Audio",
  EnumLocal.txtEditPost.name: "Edit Post",
  EnumLocal.txtEditReels.name: "Edit Reels",
  EnumLocal.txtEdit.name: "Edit",
};
