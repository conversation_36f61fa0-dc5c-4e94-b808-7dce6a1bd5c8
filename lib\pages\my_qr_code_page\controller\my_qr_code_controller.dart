import 'dart:io';

import 'package:flutter/services.dart';
import 'package:gallery_saver_plus/gallery_saver.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:shortie/custom/custom_share.dart';
import 'package:shortie/ui/loading_ui.dart';
import 'package:shortie/utils/branch_io_services.dart';
import 'package:shortie/utils/database.dart';
import 'package:shortie/utils/enums.dart';
import 'package:shortie/utils/utils.dart';
import 'package:url_launcher/url_launcher.dart';

class MyQrCodeController extends GetxController {
  ScreenshotController screenshotController = ScreenshotController();

  Future<String?> onCaptureImage() async {
    try {
      final directory = (await getApplicationDocumentsDirectory()).path;
      String fileName = DateTime.now().microsecondsSinceEpoch.toString();
      final String filePath = '$directory/$fileName.png';

      final image = await screenshotController.capture();
      if (image != null) {
        final file = File(filePath);
        await file.writeAsBytes(image);

        Utils.showLog("Capture Screen Shorts => $filePath");

        return filePath;
      } else {
        Utils.showLog("Capture Screen Shorts Failed => No image captured");
      }
    } catch (e) {
      Utils.showLog("Capture Screen Shorts Failed => $e");
    }

    return null;
  }

  Future<void> onClickDownload() async {
    Get.dialog(LoadingUi(), barrierDismissible: false); // Show Loading...
    try {
      final filePath = await onCaptureImage();
      if (filePath != null) {
        await GallerySaver.saveImage(filePath);
        Utils.showToast(EnumLocal.txtDownloadSuccess.name.tr);
      }
    } catch (e) {
      Utils.showLog("Download Screen Shorts Failed => $e");
    }
    Get.back(); // Stop Loading...
  }

  Future<void> onClickWhatsapp() async {
    Get.dialog(LoadingUi(), barrierDismissible: false); // Show Loading...
    try {
      Utils.showLog("WhatsApp share: Starting Branch.io link generation");
      
      // Create a fallback link in case Branch.io fails
      String shareText = "Check out my profile on Shortie!";
      
      try {
        // Try to generate Branch.io link with timeout
        await BranchIoServices.onCreateBranchIoLink(
          id: Database.fetchLoginUserProfileModel?.user?.id ?? "",
          name: Database.fetchLoginUserProfileModel?.user?.name ?? "",
          userId: Database.fetchLoginUserProfileModel?.user?.id ?? "",
          image: Database.fetchLoginUserProfileModel?.user?.image ?? "",
          pageRoutes: "Profile",
        );
        
        final link = await BranchIoServices.onGenerateLink();
        if (link != null) {
          shareText = link;
          Utils.showLog("WhatsApp share: Branch.io link generated successfully");
        } else {
          Utils.showLog("WhatsApp share: Branch.io link generation returned null, using fallback");
        }
      } catch (e) {
        Utils.showLog("WhatsApp share: Branch.io error, using fallback: $e");
        // Continue with fallback text
      }
      
      // Launch WhatsApp with the link (or fallback text)
      final Uri url = Uri.parse('https://wa.me/?text=$shareText');
      final bool launched = await launchUrl(url);
      
      if (!launched) {
        Utils.showLog("WhatsApp share: Failed to launch URL");
        Utils.showToast("Could not open WhatsApp");
      } else {
        Utils.showLog("WhatsApp share: URL launched successfully");
      }
    } catch (e) {
      Utils.showLog("WhatsApp share: Error: $e");
      Utils.showToast("Failed to share. Please try again.");
    } finally {
      if (Get.isDialogOpen ?? false) {
        Get.back(); // Stop Loading...
      }
    }
  }

  Future<void> onClickCopy() async {
    Get.dialog(LoadingUi(), barrierDismissible: false); // Show Loading...
    try {
      Utils.showLog("Copy: Starting Branch.io link generation");
      
      // Create a fallback link in case Branch.io fails
      String shareText = "Check out my profile on Shortie!";
      
      try {
        // Try to generate Branch.io link with timeout
        await BranchIoServices.onCreateBranchIoLink(
          id: Database.fetchLoginUserProfileModel?.user?.id ?? "",
          name: Database.fetchLoginUserProfileModel?.user?.name ?? "",
          userId: Database.fetchLoginUserProfileModel?.user?.id ?? "",
          image: Database.fetchLoginUserProfileModel?.user?.image ?? "",
          pageRoutes: "Profile",
        );
        
        final link = await BranchIoServices.onGenerateLink();
        if (link != null) {
          shareText = link;
          Utils.showLog("Copy: Branch.io link generated successfully");
        } else {
          Utils.showLog("Copy: Branch.io link generation returned null, using fallback");
        }
      } catch (e) {
        Utils.showLog("Copy: Branch.io error, using fallback: $e");
        // Continue with fallback text
      }
      
      // Copy the text to clipboard
      await Clipboard.setData(ClipboardData(text: shareText));
      Utils.showToast("Copied to clipboard");
      Utils.showLog("Copy: Text copied to clipboard");
    } catch (e) {
      Utils.showLog("Copy: Error: $e");
      Utils.showToast("Failed to copy. Please try again.");
    } finally {
      if (Get.isDialogOpen ?? false) {
        Get.back(); // Stop Loading...
      }
    }
  }

  Future<void> onClickShare() async {
    try {
      Utils.showLog("Share: Starting share process");
      Get.dialog(const LoadingUi(), barrierDismissible: false); // Start Loading...
      
      // Capture screenshot
      Utils.showLog("Share: Capturing QR code image");
      final filePath = await onCaptureImage();
      if (filePath == null) {
        throw Exception("Failed to capture QR code image");
      }
      Utils.showLog("Share: QR code image captured: $filePath");

      // Create a fallback link in case Branch.io fails
      String shareText = "Check out my profile on Shortie!";
      
      try {
        // Try to generate Branch.io link with timeout
        Utils.showLog("Share: Creating Branch.io link");
        await BranchIoServices.onCreateBranchIoLink(
          id: Database.fetchLoginUserProfileModel?.user?.id ?? "",
          name: Database.fetchLoginUserProfileModel?.user?.name ?? "",
          userId: Database.fetchLoginUserProfileModel?.user?.id ?? "",
          image: Database.fetchLoginUserProfileModel?.user?.image ?? "",
          pageRoutes: "Profile",
        );
        
        Utils.showLog("Share: Generating Branch.io URL");
        final link = await BranchIoServices.onGenerateLink();
        if (link != null) {
          shareText = link;
          Utils.showLog("Share: Branch.io link generated successfully");
        } else {
          Utils.showLog("Share: Branch.io link generation returned null, using fallback");
        }
      } catch (e) {
        Utils.showLog("Share: Branch.io error, using fallback: $e");
        // Continue with fallback text
      }

      // Share the QR code with the link
      Utils.showLog("Share: Sharing QR code with text: $shareText");
      await CustomShare.onShareFile(title: shareText, filePath: filePath);
      Utils.showLog("Share: QR code shared successfully");
    } catch (e) {
      Utils.showLog("Share: Error: $e");
      Utils.showToast("Failed to share. Please try again.");
    } finally {
      if (Get.isDialogOpen ?? false) {
        Get.back(); // Stop Loading...
        Utils.showLog("Share: Closed loading dialog");
      }
    }
  }
}
