import 'package:shortie/utils/enums.dart';

final Map<String, String> koKR = {
  EnumLocal.txtAppName.name: "쇼티",
  EnumLocal.txtSkip.name: "건너뛰다",
  EnumLocal.txtOnBoardingTitle_1.name: "세기 최고의 소셜 미디어 앱",
  EnumLocal.txtOnBoardingTitle_2.name: "세상의 모든 사람과 연결하자",
  EnumLocal.txtOnBoardingTitle_3.name: "앱에서 할 수 있는 모든 것",
  EnumLocal.txtOnBoardingSubTitle_1.name: "최첨단 기능과 직관적인 디자인으로 소셜 미디어의 미래를 경험해 보세요. 이전과는 전혀 다른 방식으로 연결하고, 공유하고, 발견할 준비를 하세요!",
  EnumLocal.txtOnBoardingSubTitle_2.name: "열정적이고 다양하며 활기찬 글로벌 커뮤니티에 참여하세요. 다른 사람들과 연결하고, 이야기를 공유하고, 세상을 더 작고 멋진 곳으로 만들어보세요!",
  EnumLocal.txtOnBoardingSubTitle_3.name: "인생의 순간을 공유하는 것부터 새로운 관심사를 발견하는 것까지, 저희 앱이 여러분을 도와드립니다. 무한한 가능성의 세계를 탐험하고, 창조하고, 성장하세요!",
  EnumLocal.txtLoginTitle.name: "주변에 새로운 사람들을 만나보자",
  EnumLocal.txtLoginSubTitle.name: "환영! 빠르게 로그인하거나 Google 계정을 사용하여 주변의 새로운 사람들을 만나보세요. 새로운 우정을 향한 여정을 시작해 보세요!",
  EnumLocal.txtQuickLogIn.name: "빠른 로그인",
  EnumLocal.txtContinueWithGoogle.name: "Google로 계속하기",
  EnumLocal.txtFillProfile.name: "프로필 작성",
  EnumLocal.txtEditProfile.name: "프로필 수정",
  EnumLocal.txtFullName.name: "성명",
  EnumLocal.txtUserName.name: "사용자 이름",
  EnumLocal.txtIdentificationCode.name: "식별 코드",
  EnumLocal.txtCountry.name: "국가",
  EnumLocal.txtBioDetails.name: "약력 세부정보",
  EnumLocal.txtGender.name: "성별",
  EnumLocal.txtMale.name: "남성",
  EnumLocal.txtFemale.name: "여성",
  EnumLocal.txtOther.name: "다른",
  EnumLocal.txtSaveProfile.name: "프로필 저장",
  EnumLocal.txtSendGift.name: "선물을 보내다",
  EnumLocal.txtSend.name: "보내다",
  EnumLocal.txtCoins.name: "동전",
  EnumLocal.txtRecharge.name: "재충전",
  EnumLocal.txtComment.name: "논평",
  EnumLocal.txtTypeComment.name: "댓글 입력...",
  EnumLocal.txtVideos.name: "비디오",
  EnumLocal.txtNext.name: "다음",
  EnumLocal.txtImages.name: "이미지",
  EnumLocal.txtAddMusic.name: "음악 추가",
  EnumLocal.txtSearchHintText.name: "찾다...",
  EnumLocal.txtDiscover.name: "발견하다",
  EnumLocal.txtFavourite.name: "가장 좋아하는",
  EnumLocal.txtPreview.name: "시사",
  EnumLocal.txtUploadReels.name: "릴 업로드",
  EnumLocal.txtWhatsOnYourMind.name: "무슨 생각을 하고 있나요?",
  EnumLocal.txtChangeThumbnail.name: "썸네일 변경",
  EnumLocal.txtAddTopic.name: "주제 추가",
  EnumLocal.txtUpload.name: "업로드",
  EnumLocal.txtAddHashtag.name: "해시태그 추가",
  EnumLocal.txtDone.name: "완료",
  EnumLocal.txtLiveStreaming.name: "라이브 스트리밍",
  EnumLocal.txtGoLive.name: "생중계",
  EnumLocal.txtLive.name: "살다",
  EnumLocal.txtStopLive.name: "라이브 중지",
  EnumLocal.txtStop.name: "멈추다",
  EnumLocal.txtStopLiveDialogText.name: "실시간 스트림을 중지하시겠습니까? 이 작업은 취소할 수 없으며 방송이 즉시 종료됩니다. '스트리밍 중지'를 클릭하여 확인하세요.",
  EnumLocal.txtFeeds.name: "피드",
  EnumLocal.txtCreate.name: "만들다",
  EnumLocal.txtSayHi.name: "인사하다",
  EnumLocal.txtReport.name: "보고서",
  EnumLocal.txtCancel.name: "취소",
  EnumLocal.txtItIsSpam.name: "스팸입니다",
  EnumLocal.txtNudityOrSexualActivity.name: "과도한 노출 또는 성행위",
  EnumLocal.txtHateSpeechOrSymbols.name: "증오심 표현 또는 상징",
  EnumLocal.txtViolenceOrDangerousOrganization.name: "폭력 또는 위험한 조직",
  EnumLocal.txtFalseInformation.name: "허위정보",
  EnumLocal.txtBullyingOrHarassment.name: "왕따 또는 괴롭힘",
  EnumLocal.txtScamOrFraud.name: "사기 또는 사기",
  EnumLocal.txtIntellectualPropertyViolation.name: "지적재산권 위반",
  EnumLocal.txtSuicideOrSelfInjury.name: "자살 또는 자해",
  EnumLocal.txtDrugs.name: "약제",
  EnumLocal.txtEatingDisorders.name: "섭식 장애",
  EnumLocal.txtSomethingElse.name: "다른 것",
  EnumLocal.txtChildAbuse.name: "아동 학대",
  EnumLocal.txtOthers.name: "기타",
  EnumLocal.txtUploadPost.name: "게시물 업로드",
  EnumLocal.txtSearch.name: "찾다",
  EnumLocal.txtHashTag.name: "해시 태그",
  EnumLocal.txtViewAll.name: "모두보기",
  EnumLocal.txtScanQRCode.name: "QR 코드 스캔",
  EnumLocal.txtScanQRCodeText.name: "프로필을 보려면 QR 코드를 스캔하세요.",
  EnumLocal.txtMyQRCodeText.name: "내 QR 코드 페이지에 오신 것을 환영합니다! 위의 QR 코드를 스캔하면 내 프로필 데이터에 즉시 액세스할 수 있습니다. 여기에는 귀하가 저에게 쉽게 연락하는 데 도움이 되는 내 연락처 정보, 소셜 미디어 프로필 및 기타 필수 세부 정보가 포함됩니다. 스마트폰 카메라로 QR 코드를 가리키기만 하면 내 개인 프로필 페이지로 이동됩니다. 빠른 스캔만으로 연결을 유지하고 나에 대해 더 자세히 알아보세요!",
  EnumLocal.txtViewProfile.name: "프로필보기",
  EnumLocal.txtViewDetails.name: "세부 정보보기",
  EnumLocal.txtFollow.name: "따르다",
  EnumLocal.txtFollowing.name: "수행원",
  EnumLocal.txtLikes.name: "좋아요",
  EnumLocal.txtFollowers.name: "추종자",
  EnumLocal.txtReels.name: "릴",
  EnumLocal.txtCollections.name: "컬렉션",
  EnumLocal.txtSettings.name: "설정",
  EnumLocal.txtAccount.name: "계정",
  EnumLocal.txtNotifyMe.name: "나를 통지",
  EnumLocal.txtLanguages.name: "언어",
  EnumLocal.txtMyWallet.name: "내 지갑",
  EnumLocal.txtShareProfile.name: "프로필 공유",
  EnumLocal.txtMyQRCode.name: "내 QR 코드",
  EnumLocal.txtVerificationRequest.name: "확인요청",
  EnumLocal.txtGeneral.name: "일반적인",
  EnumLocal.txtHelp.name: "돕다",
  EnumLocal.txtTermsOfUse.name: "이용약관",
  EnumLocal.txtPrivacyPolicy.name: "개인 정보 정책",
  EnumLocal.txtLogOut.name: "로그 아웃",
  EnumLocal.txtDeleteAccount.name: "계정 삭제",
  EnumLocal.txtDelete.name: "삭제",
  EnumLocal.txtLogOutText.name: "귀하의 계정에서 로그아웃하려고 합니다. 로그인 페이지로 리디렉션됩니다. 너 정말 계속하고 싶니?",
  EnumLocal.txtDeleteAccountText.name: "계정을 영구적으로 삭제하시겠습니까? 이 작업은 취소할 수 없으며 모든 데이터가 손실되고 관련 서비스에 액세스할 수 없게 됩니다.",
  EnumLocal.txtUploadYourImages.name: "이미지 업로드",
  EnumLocal.txtUploadIDPhotos.name: "신분증 사진 업로드",
  EnumLocal.txtPersonalPhotos.name: "개인 사진",
  EnumLocal.txtClearPhotos.name: "사진 지우기",
  EnumLocal.txtCapture.name: "포착",
  EnumLocal.txtAttach.name: "붙이다",
  EnumLocal.txtIDNumber.name: "ID 번호",
  EnumLocal.txtNameOnID.name: "신분증에 이름",
  EnumLocal.txtFullAddress.name: "전체 주소",
  EnumLocal.txtSubmit.name: "제출하다",
  EnumLocal.txtAvailableCoinBalance.name: "사용 가능한 코인 잔액",
  EnumLocal.txtRechargeCoin.name: "코인 충전",
  EnumLocal.txtCoinHistory.name: "코인의 역사",
  EnumLocal.txtWithdraw.name: "철회하다",
  EnumLocal.txtAvailableCoin.name: "사용 가능한 코인",
  EnumLocal.txtEnterCoin.name: "코인을 입력하세요",
  EnumLocal.txtEnterWithdrawCoin.name: "출금코인을 입력하세요...",
  EnumLocal.txtMinimumWithdraw.name: "*최소 인출",
  EnumLocal.txtSelectPaymentGateway.name: "결제 게이트웨이 선택",
  EnumLocal.txtSelectPaymentMethod.name: "결제 방법 선택",
  EnumLocal.txtPurchasePremiumPlanAndGetAllAccess.name: "프리미엄 플랜을 구매하고 모든 액세스 권한을 얻으세요",
  EnumLocal.txtRechargePageSubTitle.name: "고급 도구와 독점 리소스에 무제한으로 액세스하려면 프리미엄 플랜으로 업그레이드하세요. 지금 당신의 경험을 한 단계 더 높이세요.",
  EnumLocal.txtConfirmWithdrawDialogText.name: "정확성을 보장하기 위해 출금 세부정보를 주의 깊게 검토하시기 바랍니다. 오류가 발생하지 않도록 대상 계좌 정보와 출금 금액을 다시 확인하세요.",
  EnumLocal.txtPurchasePlan.name: "구매 계획",
  EnumLocal.txtPayNow.name: "지금 지불하세요",
  EnumLocal.txtMostPopularPlan.name: "가장 인기 있는 플랜",
  EnumLocal.txtComplaintOrSuggestion.name: "불만 또는 제안",
  EnumLocal.txtTypingSomethings.name: "무언가를 입력하는 중...",
  EnumLocal.txtContact.name: "연락하다",
  EnumLocal.txtAttachYourImageOrScreenshot.name: "이미지 또는 스크린샷 첨부",
  EnumLocal.txtBrowse.name: "검색",
  EnumLocal.txtNone.name: "없음",
  EnumLocal.txtChooseImage.name: "이미지 선택",
  EnumLocal.txtGallery.name: "갤러리",
  EnumLocal.txtTakePhoto.name: "사진을 찍다",
  EnumLocal.txtChooseVideo.name: "비디오 선택",
  EnumLocal.txtCreateReels.name: "릴 만들기",
  EnumLocal.txtNoDataFound.name: "데이터가 없습니다 !!",
  EnumLocal.txtTypeSomething.name: "뭔가를 입력하세요...",
  EnumLocal.txtSomeThingWentWrong.name: "뭔가 잘못됐어!!",
  EnumLocal.txtConnectionLost.name: "연결이 끊어졌습니다 !!",
  EnumLocal.txtNoInternetConnection.name: "인터넷에 연결되지 않음 !!",
  EnumLocal.txtLoginSuccess.name: "로그인 성공",
  EnumLocal.txtProfileUpdateSuccessfully.name: "프로필 업데이트가 성공적으로 완료되었습니다.",
  EnumLocal.txtReelsUploadSuccessfully.name: "릴이 성공적으로 업로드되었습니다.",
  EnumLocal.txtYouCantFollowYourOwnAccount.name: "자신의 계정을 팔로우할 수 없습니다.",
  EnumLocal.txtReportSending.name: "보고서 전송 중...",
  EnumLocal.txtReportSendSuccess.name: "전송 성공 보고",
  EnumLocal.txtYouCanSelectMaximumFiveImages.name: "최대 5개의 이미지를 선택할 수 있습니다!!",
  EnumLocal.txtPostUploading.name: "게시물 업로드 중...",
  EnumLocal.txtPostUploadSuccessfully.name: "게시물 업로드가 성공적으로 완료되었습니다.",
  EnumLocal.txtPleaseSelectProfileImage.name: "프로필 이미지를 선택해주세요!!",
  EnumLocal.txtPleaseEnterFullName.name: "이름을 입력해주세요!!",
  EnumLocal.txtPleaseEnterUserName.name: "사용자 이름을 입력해주세요!!",
  EnumLocal.txtPleaseEnterBioDetails.name: "자기소개를 입력해주세요!!",
  EnumLocal.txtPleaseAllowPermission.name: "허락을 받아주세요!!",
  EnumLocal.txtPleaseWaitSomeTime.name: "잠시만 기다려주세요...",
  EnumLocal.txtPleaseEnterCaption.name: "캡션을 입력해주세요!!",
  EnumLocal.txtPleaseSelectHashTag.name: "해시태그를 선택해주세요!!",
  EnumLocal.txtPleaseSelectPost.name: "게시물을 선택해주세요!!",
  EnumLocal.txtPleaseEnterWithdrawCoin.name: "출금코인을 입력해주세요!!",
  EnumLocal.txtPleaseSelectWithdrawMethod.name: "출금방법을 선택해주세요!!",
  EnumLocal.txtPleaseEnterAllPaymentDetails.name: "결제정보를 모두 입력해주세요!!",
  EnumLocal.txtCoinNotAvailableForSendGiftText.name: "선물을 보낼 자금이 부족합니다!!",
  EnumLocal.txtWithdrawalRequestedCoinMustBeGreaterThanSpecifiedByTheAdmin.name: "출금 요청 코인은 관리자가 지정한 코인보다 커야 합니다!!",
  EnumLocal.txtDownloadSuccess.name: "다운로드 성공",
  EnumLocal.txtPleaseEnterYourComplain.name: "불만사항을 입력해주세요!!",
  EnumLocal.txtPleaseEnterYourContactNumber.name: "연락처를 입력해주세요!!",
  EnumLocal.txtPleaseUploadScreenShort.name: "짧은 화면을 올려주세요!!",
  EnumLocal.txtYourComplainSendSuccessfully.name: "귀하의 불만 사항이 성공적으로 전송되었습니다",
  EnumLocal.txtCoinRechargeSuccess.name: "코인 충전 성공",
  EnumLocal.txtPleaseUploadProfileImage.name: "프로필 이미지를 올려주세요!!",
  EnumLocal.txtPleaseUploadDocumentImage.name: "서류 이미지를 업로드해주세요!!",
  EnumLocal.txtPleaseEnterYourIdOnNumber.name: "번호에 아이디를 입력해주세요!!",
  EnumLocal.txtPleaseEnterYourIdOnName.name: "이름에 아이디를 입력해주세요!!",
  EnumLocal.txtPleaseEnterYourIdOnAddress.name: "주소에 아이디를 입력해주세요!!",
  EnumLocal.txtVerificationRequestSendSuccessfully.name: "확인 요청이 성공적으로 전송되었습니다.",
  EnumLocal.txtLongPressToEnableAudioRecording.name: "오디오 녹음을 활성화하려면 길게 누르세요.",
  EnumLocal.txtAudioRecording.name: "오디오 녹음...",
  EnumLocal.txtOptionalInBrackets.name: "",
  EnumLocal.txtTheUserDoesNotHaveSufficientFundsToMakeTheWithdrawal.name: "",
  EnumLocal.txtVideo.name: "(선택 과목)",
  EnumLocal.txtPost.name: "사용자에게 출금을 위한 충분한 자금이 없습니다",
  EnumLocal.txtHastTag.name: "동영상",
  EnumLocal.txtCaption.name: "우편",
  EnumLocal.txtReceivedCoin.name: "HastTag",
  EnumLocal.txtSendCoin.name: "표제",
  EnumLocal.txtAddWallet.name: "받은 코인",
  EnumLocal.txtWithdrawal.name: "코인 보내기",
  EnumLocal.txtPendingWithdrawal.name: "지갑 추가",
  EnumLocal.txtCancelWithdrawal.name: "철수",
  EnumLocal.txtTypeYourHashtag.name: "해시태그를 입력하세요...",
  EnumLocal.txtYouCantSendGiftOwnVideo.name: "자신의 동영상에 선물을 보낼 수 없습니다.",
  EnumLocal.txtWelcomeBonusCoin.name: "웰컴 보너스 코인",
  EnumLocal.txtSendGiftCoin.name: "선물 코인 보내기",
  EnumLocal.txtReceiveGiftCoin.name: "선물 코인 받기",
  EnumLocal.txtYouDonHaveSufficientCoinsToSendTheGift.name: "선물을 보낼 코인이 부족합니다!!",
  EnumLocal.txtEnterYourTextWithHashtag.name: "해시태그와 함께 텍스트를 입력하세요...",
  EnumLocal.txtDeleteAll.name: "모두 삭제",
  EnumLocal.txtRequests.name: "요구",
  EnumLocal.txtDecline.name: "감소",
  EnumLocal.txtAccept.name: "수용하다",
  EnumLocal.txtIgnore.name: "무시하다",
  EnumLocal.txtAcceptMessageRequestFrom.name: "메시지 요청 수락",
  EnumLocal.txtUploadYourFirstVideo.name: "메시지 요청 수락",
  EnumLocal.txtEnterVerificationCode.name: "첫 번째 동영상을 업로드하세요.",
  EnumLocal.txtEnterVerificationCodeAsUnder.name: "\n인증 코드 입력",
  EnumLocal.txtSendCodeThisNumber.name: "아래와 같이 인증번호를 입력하세요.",
  EnumLocal.txtResendCode.name: "이 번호로 코드 보내줘",
  EnumLocal.EnterMobileNumber.name: "코드 재전송",
  EnumLocal.txtEnterYourMobileNumberHereAndContinue.name: "휴대폰 번호 입력\n",
  EnumLocal.txtVerify.name: "여기에 휴대폰 번호를 입력하고 계속하세요...",
  EnumLocal.txtMobileNumHintTest.name: "확인하다",
  EnumLocal.txtPleaseEnterCorrectMobileNumber.name: "휴대폰 번호를 입력하세요",
  EnumLocal.txtPleaseEnterMobileNumber.name: "정확한 휴대폰 번호를 입력해주세요",
  EnumLocal.txtVerificationTimeout.name: "휴대폰 번호를 입력해주세요",
  EnumLocal.txtVerificationCodeSend.name: "확인 시간 초과",
  EnumLocal.txtPleaseEnterVerificationCode.name: "인증번호가 전송됩니다.",
  EnumLocal.txtMoreOption.name: "인증코드를 입력해주세요",
  EnumLocal.txtDeletePost.name: "추가 옵션",
  EnumLocal.txtDeleteVideo.name: "게시물 삭제",
  EnumLocal.txtDeletePostVideoContent.name: "비디오 삭제",
  EnumLocal.txtMobile.name: "이동하는",
  EnumLocal.txtGoogle.name: "Google",
  EnumLocal.txtOr.name: "또는",
  EnumLocal.txtUseAudio.name: "오디오 사용",
  EnumLocal.txtAudio.name: "오디오",
  EnumLocal.txtEditPost.name: "게시물 수정",
  EnumLocal.txtEditReels.name: "릴 편집",
  EnumLocal.txtEdit.name: "편집하다",
};
