import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:screenshot/screenshot.dart';
import 'package:shortie/custom/custom_share.dart';
import 'package:shortie/main.dart';
import 'package:shortie/ui/loading_ui.dart';
import 'package:shortie/pages/setting_page/api/delete_user_api.dart';
import 'package:shortie/pages/setting_page/model/delete_user_model.dart';
import 'package:shortie/ui/preview_network_image_ui.dart';
import 'package:shortie/utils/asset.dart';
import 'package:shortie/utils/branch_io_services.dart';
import 'package:shortie/utils/color.dart';
import 'package:shortie/utils/database.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:shortie/utils/font_style.dart';
import 'package:shortie/utils/utils.dart';

class SettingController extends GetxController {
  bool isShowNotification = false;

  ScreenshotController screenshotController = ScreenshotController();

  DeleteUserModel? deleteUserModel;

  @override
  void onInit() {
    isShowNotification = Database.isShowNotification;
    super.onInit();
  }

  Future<void> onSwitchNotification() async {
    isShowNotification = !isShowNotification;
    Database.onSetNotification(isShowNotification);
    update(["onSwitchNotification"]);
  }

  Future<void> onDeleteAccount() async {
    Get.back(); // Close Dialog...

    Get.dialog(const LoadingUi(), barrierDismissible: false); // Start Loading...

    deleteUserModel = await DeleteUserApi.callApi(loginUserId: Database.loginUserId);

    Get.back(); // Stop Loading...

    if (deleteUserModel?.status ?? false) {
      Database.onLogOut();
    }
  }

  Future<String?> onCaptureImage() async {
    try {
      final directory = (await getApplicationDocumentsDirectory()).path;
      String fileName = DateTime.now().microsecondsSinceEpoch.toString();
      final String filePath = '$directory/$fileName.png';

      final image = await screenshotController.capture();
      if (image != null) {
        final file = File(filePath);
        await file.writeAsBytes(image);

        Utils.showLog("Capture Screen Shorts => $filePath");

        return filePath;
      } else {
        Utils.showLog("Capture Screen Shorts Failed => No image captured");
      }
    } catch (e) {
      Utils.showLog("Capture Screen Shorts Failed => $e");
    }

    return null;
  }

  // Future<void> onClickShareProfile() async {
  //   Get.dialog(const LoadingUi(), barrierDismissible: false); // Start Loading...
  //   final filePath = await onCaptureImage();

  //   await BranchIoServices.onCreateBranchIoLink(
  //     id: Database.fetchLoginUserProfileModel?.user?.id ?? "",
  //     name: Database.fetchLoginUserProfileModel?.user?.name ?? "",
  //     userId: Database.fetchLoginUserProfileModel?.user?.id ?? "",
  //     image: Database.fetchLoginUserProfileModel?.user?.image ?? "",
  //     pageRoutes: "Profile",
  //   );
  //   final link = await BranchIoServices.onGenerateLink();

  //   if (link != null && filePath != null) {
  //     CustomShare.onShareFile(title: link, filePath: filePath);
  //   }

  //   Get.back(); // Stop Loading...
  // }


  Future<void> onClickShareProfile() async {
    // Create a timeout completer to ensure we don't hang indefinitely
    final timeoutCompleter = Completer<bool>();
    Timer? timeoutTimer;
    
    try {
      Utils.showLog("Starting profile sharing process");
      Get.dialog(const LoadingUi(), barrierDismissible: false);
      
      // Set a timeout to ensure we don't hang indefinitely
      timeoutTimer = Timer(const Duration(seconds: 15), () {
        if (!timeoutCompleter.isCompleted) {
          timeoutCompleter.complete(true);
          Utils.showLog("Profile sharing timed out");
          throw TimeoutException("Profile sharing operation timed out");
        }
      });
      
      // Check if user profile data exists
      Utils.showLog("Checking user profile data");
      if (Database.fetchLoginUserProfileModel?.user == null) {
        throw Exception("User profile data not found");
      }

      final user = Database.fetchLoginUserProfileModel!.user!;
      Utils.showLog("User data found: ${user.id}, ${user.name}");
      
      // Capture screenshot
      Utils.showLog("Capturing screenshot");
      final filePath = await onCaptureImage();
      if (filePath == null) {
        throw Exception("Failed to capture profile image");
      }
      Utils.showLog("Screenshot captured: $filePath");

      // Try to generate Branch.io link with fallback
      String shareText = "Check out my profile on Shortie!";
      
      try {
        Utils.showLog("Creating Branch.io link");
        await BranchIoServices.onCreateBranchIoLink(
          id: user.id ?? "",
          name: user.name ?? "",
          userId: user.id ?? "",
          image: user.image ?? "",
          pageRoutes: "Profile",
        );
        
        Utils.showLog("Branch.io link created, generating URL");
        final link = await BranchIoServices.onGenerateLink();
        if (link != null) {
          shareText = link;
          Utils.showLog("Branch.io link generated: $link");
        } else {
          Utils.showLog("Branch.io link generation failed, using fallback text");
        }
      } catch (e) {
        Utils.showLog("Error generating Branch.io link, using fallback: $e");
        // Continue with fallback text
      }

      // Share the profile
      Utils.showLog("Sharing profile");
      await CustomShare.onShareFile(title: shareText, filePath: filePath);
      Utils.showLog("Profile shared successfully");
      
      // Complete the timeout completer to indicate success
      if (!timeoutCompleter.isCompleted) {
        timeoutCompleter.complete(false);
      }
    } catch (e) {
      Utils.showLog("Error sharing profile: $e");
      // Using Get.snackbar instead of non-existent Utils.showError
      Get.snackbar(
        'Error',
        'Failed to share profile. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      // Cancel the timeout timer
      timeoutTimer?.cancel();
      
      Utils.showLog("Cleaning up share process");
      if (Get.isDialogOpen ?? false) {
        Get.back(); // Dismiss loading dialog
        Utils.showLog("Closed loading dialog");
      }
    }
  }



  Widget profileImage() {
    return Screenshot(
      controller: screenshotController,
      child: Container(
        width: Get.width,
        height: 350,
        margin: EdgeInsets.symmetric(horizontal: Get.width / 7),
        decoration: BoxDecoration(
          gradient: AppColor.primaryLinearGradient,
          borderRadius: BorderRadius.circular(45),
        ),
        child: Column(
          children: [
            40.height,
            QrImageView(
              data: "${Database.loginUserId},${true}",
              version: QrVersions.auto,
              size: 140.0,
              eyeStyle: QrEyeStyle(color: AppColor.white, eyeShape: QrEyeShape.square),
              dataModuleStyle: QrDataModuleStyle(color: AppColor.white, dataModuleShape: QrDataModuleShape.square),
            ),
            12.height,
            Divider(
              indent: 30,
              endIndent: 30,
              thickness: 0.5,
              color: AppColor.white.withOpacity(0.5),
            ),
            Container(
              padding: EdgeInsets.all(1.5),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: AppColor.white, width: 1),
              ),
              child: Container(
                width: 60,
                clipBehavior: Clip.antiAlias,
                decoration: BoxDecoration(shape: BoxShape.circle),
                child: Stack(
                  children: [
                    AspectRatio(
                      aspectRatio: 1,
                      child: Image.asset(AppAsset.icProfilePlaceHolder),
                    ),
                    AspectRatio(
                      aspectRatio: 1,
                      child: PreviewNetworkImageUi(image: Database.fetchLoginUserProfileModel?.user?.image ?? ""),
                    ),
                  ],
                ),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  Database.fetchLoginUserProfileModel?.user?.name ?? "",
                  style: AppFontStyle.styleW700(AppColor.white, 18),
                ),
                Visibility(
                  visible: Database.fetchLoginUserProfileModel?.user?.isVerified ?? false,
                  child: Padding(
                    padding: const EdgeInsets.only(left: 3),
                    child: Image.asset(AppAsset.icBlueTick, width: 20),
                  ),
                ),
              ],
            ),
            Text(
              Database.fetchLoginUserProfileModel?.user?.userName ?? "",
              style: AppFontStyle.styleW400(AppColor.white, 13),
            ),
            20.height,
          ],
        ),
      ),
    );
  }
}
