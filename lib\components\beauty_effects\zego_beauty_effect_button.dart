// Flutter imports:
import 'package:flutter/material.dart';

import '../../internal/internal_defines.dart';
import '../common/button_icon_widget.dart';


class ZegoBeautyEffectButton extends StatefulWidget {
  const ZegoBeautyEffectButton({
    Key? key,
    this.iconSize,
    this.buttonSize,
    this.icon,
    this.onPressed,
  }) : super(key: key);

  final Size? iconSize;
  final Size? buttonSize;
  final ButtonIcon? icon;

  ///  You can do what you want after pressed.
  final void Function()? onPressed;

  @override
  State<StatefulWidget> createState() => _ZegoBeautyEffectButtonState();
}

class _ZegoBeautyEffectButtonState extends State<ZegoBeautyEffectButton> {
  @override
  Widget build(BuildContext context) {
    final containerSize = widget.buttonSize ?? const Size(96, 96);
    final iconSize = widget.iconSize ?? const Size(56, 56);
    
    return GestureDetector(
      onTap: widget.onPressed,
      child: Container(
        width: containerSize.width,
        height: containerSize.height,
        decoration: BoxDecoration(
          color: const Color(0xff2C2F3E).withOpacity(0.6),
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white.withOpacity(0.3), width: 1),
        ),
        child: Center(
          child: widget.icon != null
              ? ButtonIconWidget(
                  buttonIcon: widget.icon,
                  size: iconSize.width,
                )
              : const Icon(
                  Icons.face_retouching_natural,
                  color: Colors.white,
                  size: 32,
                ),
        ),
      ),
    );
  }
}
