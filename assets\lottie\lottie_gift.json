{"v": "5.7.11", "fr": 25, "ip": 0, "op": 61, "w": 350, "h": 350, "nm": "Comp 1", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "NULL ", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [175, 175, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [50, 50, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [128, 128, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 125, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Glitter 5", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [51.123, 8.487, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [20, 20, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Rotation Speed", "np": 3, "mn": "ADBE Angle Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "<PERSON><PERSON>", "mn": "ADBE Angle Control-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 25, "s": [15]}, {"t": 45.0205078125, "s": [10]}], "ix": 6}, "is": {"a": 0, "k": 234, "ix": 8}, "or": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.52], "y": [0]}, "t": 25, "s": [33]}, {"t": 45.0205078125, "s": [200]}], "ix": 7}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "Polystar Path 1", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1 - AC Color 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.51, 0.51], "y": [1, 1]}, "o": {"x": [0.49, 0.49], "y": [0, 0]}, "t": 25, "s": [0, 0]}, {"i": {"x": [0.51, 0.51], "y": [1, 1]}, "o": {"x": [0.49, 0.49], "y": [0, 0]}, "t": 30.005, "s": [100, 100]}, {"t": 45.8544921875, "s": [0, 0]}], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6, "x": "var $bm_rt;\n$bm_rt = $bm_sum(value, $bm_mul(time, effect('Rotation Speed')('ADBE Angle Control-0001')));"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Polystar 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 25, "op": 46.6883550216884, "st": 25, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Glitter 4", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1.123, 53.737, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [20, 20, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Rotation Speed", "np": 3, "mn": "ADBE Angle Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "<PERSON><PERSON>", "mn": "ADBE Angle Control-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 18, "s": [15]}, {"t": 38.0205078125, "s": [10]}], "ix": 6}, "is": {"a": 0, "k": 234, "ix": 8}, "or": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.52], "y": [0]}, "t": 18, "s": [33]}, {"t": 38.0205078125, "s": [200]}], "ix": 7}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "Polystar Path 1", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1 - AC Color 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.51, 0.51], "y": [1, 1]}, "o": {"x": [0.49, 0.49], "y": [0, 0]}, "t": 18, "s": [0, 0]}, {"i": {"x": [0.51, 0.51], "y": [1, 1]}, "o": {"x": [0.49, 0.49], "y": [0, 0]}, "t": 23.005, "s": [100, 100]}, {"t": 38.8544921875, "s": [0, 0]}], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6, "x": "var $bm_rt;\n$bm_rt = $bm_sum(value, $bm_mul(time, effect('Rotation Speed')('ADBE Angle Control-0001')));"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Polystar 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 18, "op": 39.6883550216884, "st": 18, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Glitter 3", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [40.373, 86.237, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [20, 20, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Rotation Speed", "np": 3, "mn": "ADBE Angle Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "<PERSON><PERSON>", "mn": "ADBE Angle Control-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 28, "s": [15]}, {"t": 48.0205078125, "s": [10]}], "ix": 6}, "is": {"a": 0, "k": 234, "ix": 8}, "or": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.52], "y": [0]}, "t": 28, "s": [33]}, {"t": 48.0205078125, "s": [200]}], "ix": 7}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "Polystar Path 1", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1 - AC Color 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.51, 0.51], "y": [1, 1]}, "o": {"x": [0.49, 0.49], "y": [0, 0]}, "t": 28, "s": [0, 0]}, {"i": {"x": [0.51, 0.51], "y": [1, 1]}, "o": {"x": [0.49, 0.49], "y": [0, 0]}, "t": 33.005, "s": [100, 100]}, {"t": 48.8544921875, "s": [0, 0]}], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6, "x": "var $bm_rt;\n$bm_rt = $bm_sum(value, $bm_mul(time, effect('Rotation Speed')('ADBE Angle Control-0001')));"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Polystar 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 28, "op": 49.6883550216884, "st": 28, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Glitter 2", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [94.531, 60.998, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [20, 20, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Rotation Speed", "np": 3, "mn": "ADBE Angle Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "<PERSON><PERSON>", "mn": "ADBE Angle Control-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [15]}, {"t": 42.0205078125, "s": [10]}], "ix": 6}, "is": {"a": 0, "k": 234, "ix": 8}, "or": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.52], "y": [0]}, "t": 22, "s": [33]}, {"t": 42.0205078125, "s": [200]}], "ix": 7}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "Polystar Path 1", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1 - AC Color 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.51, 0.51], "y": [1, 1]}, "o": {"x": [0.49, 0.49], "y": [0, 0]}, "t": 22, "s": [0, 0]}, {"i": {"x": [0.51, 0.51], "y": [1, 1]}, "o": {"x": [0.49, 0.49], "y": [0, 0]}, "t": 27.005, "s": [100, 100]}, {"t": 42.8544921875, "s": [0, 0]}], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6, "x": "var $bm_rt;\n$bm_rt = $bm_sum(value, $bm_mul(time, effect('Rotation Speed')('ADBE Angle Control-0001')));"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Polystar 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 22, "op": 43.6883550216884, "st": 22, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Glitter 1", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [87.281, 32, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [20, 20, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Rotation Speed", "np": 3, "mn": "ADBE Angle Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "<PERSON><PERSON>", "mn": "ADBE Angle Control-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 15, "s": [15]}, {"t": 35.0205078125, "s": [10]}], "ix": 6}, "is": {"a": 0, "k": 234, "ix": 8}, "or": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.52], "y": [0]}, "t": 15, "s": [33]}, {"t": 35.0205078125, "s": [200]}], "ix": 7}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "Polystar Path 1", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1 - AC Color 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.51, 0.51], "y": [1, 1]}, "o": {"x": [0.49, 0.49], "y": [0, 0]}, "t": 15, "s": [0, 0]}, {"i": {"x": [0.51, 0.51], "y": [1, 1]}, "o": {"x": [0.49, 0.49], "y": [0, 0]}, "t": 20.005, "s": [100, 100]}, {"t": 35.8544921875, "s": [0, 0]}], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6, "x": "var $bm_rt;\n$bm_rt = $bm_sum(value, $bm_mul(time, effect('Rotation Speed')('ADBE Angle Control-0001')));"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Polystar 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 15, "op": 36.6883550216884, "st": 15, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Layer 1", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 5, "s": [-9]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 10, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 15, "s": [9]}, {"t": 20, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.41, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [50, 50, 0], "to": [0, -5.833, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.56, "y": 0}, "t": 10, "s": [50, 15, 0], "to": [0, 0, 0], "ti": [0, -5.833, 0]}, {"t": 20, "s": [50, 50, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "<PERSON><PERSON><PERSON>", "np": 34, "mn": "<PERSON><PERSON>udo/<PERSON> v3.2", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "Smart Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 7, "nm": "Follow Through", "mn": "P<PERSON>udo/<PERSON> v3.2-0003", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 6, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0004", "ix": 4, "v": 0}, {"ty": 0, "nm": "Duration (s)", "mn": "P<PERSON>udo/<PERSON> v3.2-0005", "ix": 5, "v": {"a": 0, "k": 0.3, "ix": 5}}, {"ty": 0, "nm": "Amplitude", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0006", "ix": 6, "v": {"a": 0, "k": 50, "ix": 6}}, {"ty": 6, "nm": "", "mn": "P<PERSON>udo/<PERSON> v3.2-0007", "ix": 7, "v": 0}, {"ty": 6, "nm": "Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0008", "ix": 8, "v": 0}, {"ty": 0, "nm": "Slow In", "mn": "P<PERSON>udo/<PERSON> v3.2-0009", "ix": 9, "v": {"a": 0, "k": 60, "ix": 9}}, {"ty": 0, "nm": "Slow Out", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0010", "ix": 10, "v": {"a": 0, "k": 25, "ix": 10}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0011", "ix": 11, "v": 0}, {"ty": 6, "nm": "Follow Through", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0012", "ix": 12, "v": 0}, {"ty": 0, "nm": "Elasticity", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0013", "ix": 13, "v": {"a": 0, "k": 10, "ix": 13}}, {"ty": 0, "nm": "Elasticity random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14}}, {"ty": 0, "nm": "Damping", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0015", "ix": 15, "v": {"a": 0, "k": 50, "ix": 15}}, {"ty": 0, "nm": "Damping random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0016", "ix": 16, "v": {"a": 0, "k": 0, "ix": 16}}, {"ty": 7, "nm": "<PERSON><PERSON><PERSON>", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0017", "ix": 17, "v": {"a": 0, "k": 0, "ix": 17}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0018", "ix": 18, "v": 0}, {"ty": 6, "nm": "Spatial Options", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0019", "ix": 19, "v": 0}, {"ty": 7, "nm": "Smart Interpolation", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0020", "ix": 20, "v": {"a": 0, "k": 0, "ix": 20}}, {"ty": 7, "nm": "Mode", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0021", "ix": 21, "v": {"a": 0, "k": 1, "ix": 21}}, {"ty": 6, "nm": "Overlap (simulation)", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0022", "ix": 22, "v": 0}, {"ty": 7, "nm": "Overlap", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0023", "ix": 23, "v": {"a": 0, "k": 1, "ix": 23}}, {"ty": 0, "nm": "Delay (s)", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0024", "ix": 24, "v": {"a": 0, "k": 0.05, "ix": 24}}, {"ty": 0, "nm": "Overlap random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0025", "ix": 25, "v": {"a": 0, "k": 0, "ix": 25}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0026", "ix": 26, "v": 0}, {"ty": 6, "nm": "Soft Body (simulation)", "mn": "<PERSON>seudo/<PERSON> v3.2-0027", "ix": 27, "v": 0}, {"ty": 7, "nm": "Soft Body", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0028", "ix": 28, "v": {"a": 0, "k": 1, "ix": 28}}, {"ty": 0, "nm": "Soft-Body Flexibility", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0029", "ix": 29, "v": {"a": 0, "k": 100, "ix": 29}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0030", "ix": 30, "v": 0}, {"ty": 6, "nm": "", "mn": "Pseudo/<PERSON><PERSON> v3.2-0031", "ix": 31, "v": 0}, {"ty": 0, "nm": "Precision", "mn": "Pseudo/<PERSON> v3.2-0032", "ix": 32, "v": {"a": 0, "k": 1, "ix": 32}}]}, {"ty": 5, "nm": "Kleaner 2", "np": 34, "mn": "<PERSON><PERSON>udo/<PERSON> v3.2", "ix": 2, "en": 1, "ef": [{"ty": 7, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "Smart Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 7, "nm": "Follow Through", "mn": "P<PERSON>udo/<PERSON> v3.2-0003", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 6, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0004", "ix": 4, "v": 0}, {"ty": 0, "nm": "Duration (s)", "mn": "P<PERSON>udo/<PERSON> v3.2-0005", "ix": 5, "v": {"a": 0, "k": 0.3, "ix": 5}}, {"ty": 0, "nm": "Amplitude", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0006", "ix": 6, "v": {"a": 0, "k": 50, "ix": 6}}, {"ty": 6, "nm": "", "mn": "P<PERSON>udo/<PERSON> v3.2-0007", "ix": 7, "v": 0}, {"ty": 6, "nm": "Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0008", "ix": 8, "v": 0}, {"ty": 0, "nm": "Slow In", "mn": "P<PERSON>udo/<PERSON> v3.2-0009", "ix": 9, "v": {"a": 0, "k": 60, "ix": 9}}, {"ty": 0, "nm": "Slow Out", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0010", "ix": 10, "v": {"a": 0, "k": 25, "ix": 10}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0011", "ix": 11, "v": 0}, {"ty": 6, "nm": "Follow Through", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0012", "ix": 12, "v": 0}, {"ty": 0, "nm": "Elasticity", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0013", "ix": 13, "v": {"a": 0, "k": 10, "ix": 13}}, {"ty": 0, "nm": "Elasticity random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14}}, {"ty": 0, "nm": "Damping", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0015", "ix": 15, "v": {"a": 0, "k": 50, "ix": 15}}, {"ty": 0, "nm": "Damping random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0016", "ix": 16, "v": {"a": 0, "k": 0, "ix": 16}}, {"ty": 7, "nm": "<PERSON><PERSON><PERSON>", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0017", "ix": 17, "v": {"a": 0, "k": 0, "ix": 17}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0018", "ix": 18, "v": 0}, {"ty": 6, "nm": "Spatial Options", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0019", "ix": 19, "v": 0}, {"ty": 7, "nm": "Smart Interpolation", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0020", "ix": 20, "v": {"a": 0, "k": 0, "ix": 20}}, {"ty": 7, "nm": "Mode", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0021", "ix": 21, "v": {"a": 0, "k": 1, "ix": 21}}, {"ty": 6, "nm": "Overlap (simulation)", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0022", "ix": 22, "v": 0}, {"ty": 7, "nm": "Overlap", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0023", "ix": 23, "v": {"a": 0, "k": 1, "ix": 23}}, {"ty": 0, "nm": "Delay (s)", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0024", "ix": 24, "v": {"a": 0, "k": 0.05, "ix": 24}}, {"ty": 0, "nm": "Overlap random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0025", "ix": 25, "v": {"a": 0, "k": 0, "ix": 25}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0026", "ix": 26, "v": 0}, {"ty": 6, "nm": "Soft Body (simulation)", "mn": "<PERSON>seudo/<PERSON> v3.2-0027", "ix": 27, "v": 0}, {"ty": 7, "nm": "Soft Body", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0028", "ix": 28, "v": {"a": 0, "k": 1, "ix": 28}}, {"ty": 0, "nm": "Soft-Body Flexibility", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0029", "ix": 29, "v": {"a": 0, "k": 100, "ix": 29}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0030", "ix": 30, "v": 0}, {"ty": 6, "nm": "", "mn": "Pseudo/<PERSON><PERSON> v3.2-0031", "ix": 31, "v": 0}, {"ty": 0, "nm": "Precision", "mn": "Pseudo/<PERSON> v3.2-0032", "ix": 32, "v": {"a": 0, "k": 1, "ix": 32}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.527, -0.139], [-3.818, 0], [-3.748, 0.486], [0.277, 1.527], [0, 0], [1.11, -0.277], [3.818, 0], [2.707, 0.764], [0.208, -1.11]], "o": [[-0.277, 1.527], [3.748, 0.486], [3.818, 0], [1.527, -0.139], [0, 0], [-0.208, -1.11], [-2.707, 0.764], [-3.818, 0], [-1.11, -0.277], [0, 0]], "v": [[-13.621, -45.36], [-11.26, -42.237], [-0.016, -41.334], [11.229, -42.237], [13.59, -45.36], [11.646, -55.842], [9.147, -57.369], [-0.016, -56.258], [-9.178, -57.369], [-11.677, -55.842]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.831372559071, 0.458823531866, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.735, -0.208], [-4.165, 0], [-4.095, 0.486], [0.277, 1.666], [0, 0], [1.249, -0.347], [4.165, 0], [2.985, 0.764], [0.208, -1.25]], "o": [[-0.277, 1.666], [4.096, 0.486], [4.165, 0], [1.735, -0.208], [0, 0], [-0.208, -1.25], [-2.985, 0.764], [-4.165, 0], [-1.249, -0.347], [0, 0]], "v": [[-14.94, -38.141], [-12.371, -34.671], [-0.016, -33.699], [12.34, -34.671], [14.909, -38.141], [12.757, -49.594], [10.05, -51.261], [-0.016, -50.15], [-10.081, -51.261], [-12.788, -49.594]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.72549021244, 0.380392163992, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.281, -0.281], [-0.235, -0.281], [-0.187, -0.281], [-0.282, -0.516], [-0.047, -0.188], [-0.047, -0.187], [0, -0.234], [0.047, -0.235], [0.281, -0.328], [0.094, -0.094], [0.047, -0.047], [0.047, 0], [0.375, -0.187], [-0.703, -0.094], [-0.14, 0], [-1.922, -0.188], [-2.344, 1.031], [-2.063, -0.938], [-1.875, 0.234], [-0.141, 0], [-0.562, 0.375], [0.234, 0.281], [0.094, 0.094], [0, 0.844], [-0.047, 0.235], [-1.031, 1.172], [-0.234, 0.234], [-0.328, 0.234], [-0.14, 0.14], [0, 0], [0, 0], [1.078, 0.609], [0.094, -0.187], [-0.75, -2.812], [-2.063, -2.156], [0.094, -0.891], [3.609, 1.828], [3.422, -1.828], [0.609, 0.891], [-0.89, 3.187], [3, 3.797], [0.141, 0.14], [0.047, 0], [0.234, -1.266], [0, 0]], "o": [[0.328, 0.281], [0.328, 0.235], [0.281, 0.281], [0.469, 0.562], [0.094, 0.234], [0.094, 0.234], [0.047, 0.235], [0, 0.234], [-0.094, 0.422], [-0.047, 0.094], [-0.047, 0.047], [-0.047, 0.047], [-0.234, 0.281], [0.609, 0.375], [0.14, 0], [1.828, 0.234], [2.016, -0.938], [2.391, 1.031], [1.875, -0.188], [0.141, 0], [0.703, -0.094], [-0.375, -0.187], [-0.14, -0.047], [-0.562, -0.562], [0, -0.234], [0.234, -0.984], [0.187, -0.235], [0.281, -0.281], [0.14, -0.187], [0, 0], [0, 0], [-0.234, -1.266], [-0.14, 0.14], [-3, 3.797], [0.891, 3.187], [-0.562, 0.891], [-3.422, -1.781], [-3.609, 1.828], [-0.094, -0.891], [2.063, -2.109], [0.797, -2.812], [-0.094, -0.187], [0, 0], [-1.078, 0.609], [0, 0], [0, 0]], "v": [[-16.575, -42.078], [-15.684, -41.234], [-14.887, -40.437], [-14.184, -39.64], [-13.106, -38.046], [-12.871, -37.437], [-12.684, -36.828], [-12.59, -36.125], [-12.684, -35.421], [-13.246, -34.343], [-13.434, -34.062], [-13.528, -33.921], [-13.668, -33.781], [-14.606, -33.031], [-12.637, -32.328], [-12.215, -32.281], [-6.59, -31.672], [-0.028, -34.625], [6.582, -31.672], [12.207, -32.281], [12.629, -32.328], [14.597, -33.031], [13.66, -33.781], [13.379, -34.062], [12.535, -36.125], [12.629, -36.828], [14.55, -40.109], [15.207, -40.812], [16.097, -41.656], [16.566, -42.125], [16.144, -44.422], [13.941, -56.281], [11.832, -59.187], [11.457, -58.672], [7.472, -48.219], [11.597, -39.64], [10.566, -36.922], [-0.028, -42.359], [-10.574, -36.922], [-11.653, -39.64], [-7.528, -48.219], [-11.465, -58.672], [-11.84, -59.187], [-11.887, -59.187], [-13.996, -56.281], [-16.153, -44.422]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.066666670144, 0.113725490868, 0.20000000298, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 10, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -6.039], [1.457, -2.152], [4.859, 0], [7.497, -6.178], [0, 0], [0.069, 0.069], [0, 0.833], [-2.916, 2.36], [-4.443, 0.208]], "o": [[0, 2.499], [-0.416, -5.692], [-4.72, 0.139], [0, 0], [-0.139, -0.069], [-0.555, -0.555], [0, -1.735], [7.08, -5.831], [4.929, 0]], "v": [[49.129, -42.514], [47.394, -35.087], [38.301, -44.874], [13.728, -33.838], [13.659, -33.768], [13.381, -34.046], [12.548, -36.128], [17.13, -42.584], [40.383, -53.065]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.866666674614, 0.521568655968, 0.211764708161, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4.931, 0], [2.85, 3.308], [0, 0.813], [-2.905, 2.325], [-4.45, 0.176], [0, -6.013], [7.518, -1.071]], "o": [[-3.783, 0], [-0.552, -0.603], [0, -1.756], [7.101, -5.857], [4.945, 0], [0, 4.718], [-1.427, 0.19]], "v": [[30.042, -30.715], [13.359, -34.023], [12.546, -36.131], [17.115, -42.55], [40.396, -53.064], [49.13, -42.516], [40.647, -31.175]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.952941179276, 0.635294139385, 0.313725501299, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -0.281], [-0.844, -0.891], [-0.14, -0.094], [-5.39, -0.234], [-1.547, 0], [-1.547, 0.234], [0, 2.906], [0.047, 0.469], [5.766, 0], [7.125, -5.719], [0.094, -0.094], [0.938, -1.547], [0.094, -0.891]], "o": [[0, 1.406], [0.14, 0.141], [2.578, 2.484], [1.594, 0.047], [5.109, 0], [10.266, -1.453], [0, -0.516], [-0.562, -6.328], [-5.109, 0.234], [-0.094, 0.047], [-1.781, 1.453], [-0.562, 0.891], [-0.094, 0.281]], "v": [[10.472, -36.125], [11.785, -32.656], [12.207, -32.281], [25.332, -28.718], [30.019, -28.625], [40.941, -29.14], [51.207, -42.5], [51.113, -44], [40.332, -55.156], [16.144, -44.422], [15.816, -44.187], [11.597, -39.64], [10.566, -36.922]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.72549021244, 0.380392163992, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 2, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -1.666], [1.735, 3.193], [4.998, 0], [0.833, -0.208], [5.484, -6.941], [0.139, -0.208], [0.555, -2.221], [0.139, 0.625], [-2.985, 3.818], [-0.208, 0.208], [-4.512, 1.458], [-0.833, 0], [-2.777, -5.345], [-0.902, -5.137]], "o": [[-0.902, -5.137], [-2.777, -5.345], [-0.833, 0], [-4.512, 1.458], [-0.208, 0.208], [-1.943, 2.499], [-0.208, -0.625], [-0.763, -2.846], [0.139, -0.208], [5.484, -6.941], [0.833, -0.208], [4.998, 0], [1.735, 3.193], [0.347, 1.735]], "v": [[51.142, -46.888], [46.561, -60.284], [34.136, -69.794], [31.637, -69.447], [12.548, -54.176], [11.993, -53.482], [8.036, -46.332], [7.481, -48.206], [11.438, -58.688], [11.993, -59.382], [31.082, -74.653], [33.581, -75], [46.006, -65.49], [50.587, -52.094]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.870588243008, 0.607843160629, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 2, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.75, -2.812], [-2.063, -2.156], [-1.547, -0.375], [-0.656, -0.047], [-1.406, 0.422], [-1.406, 0.656], [-0.328, 3.937], [0.516, 2.812], [1.734, 3.187], [5.016, 0], [0.797, -0.188], [5.484, -6.937], [0.047, -0.094], [0.094, -0.187]], "o": [[0.891, 3.187], [1.031, 1.078], [0.562, 0.141], [1.172, 0.047], [4.875, -1.359], [3.375, -1.406], [0.281, -2.531], [-0.891, -5.109], [-2.766, -5.344], [-0.844, 0], [-4.547, 1.453], [-0.047, 0.047], [-0.14, 0.14], [-3, 3.797]], "v": [[7.472, -48.219], [11.597, -39.64], [15.394, -37.39], [17.222, -37.109], [21.066, -37.672], [45.863, -35.375], [51.113, -44], [50.597, -52.109], [46.004, -65.468], [33.582, -74.984], [31.097, -74.656], [11.972, -59.375], [11.832, -59.187], [11.457, -58.672]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.831372559071, 0.458823531866, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 2, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -6.039], [-1.457, -2.152], [-4.859, 0], [-7.497, -6.178], [0, 0], [-0.069, 0.069], [0, 0.833], [2.916, 2.36], [4.443, 0.208]], "o": [[0, 2.499], [0.416, -5.692], [4.72, 0.139], [0, 0], [0.139, -0.069], [0.555, -0.555], [0, -1.735], [-7.08, -5.831], [-4.929, 0]], "v": [[-49.16, -42.514], [-47.425, -35.087], [-38.332, -44.874], [-13.759, -33.838], [-13.69, -33.768], [-13.412, -34.046], [-12.579, -36.128], [-17.161, -42.584], [-40.414, -53.065]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.866666674614, 0.521568655968, 0.211764708161, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 9", "np": 2, "cix": 2, "bm": 0, "ix": 9, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-4.931, 0], [-2.85, 3.308], [0, 0.813], [2.905, 2.325], [4.45, 0.176], [0, -6.013], [-7.518, -1.071]], "o": [[3.783, 0], [0.552, -0.603], [0, -1.756], [-7.101, -5.857], [-4.945, 0], [0, 4.718], [1.427, 0.19]], "v": [[-30.073, -30.715], [-13.39, -34.023], [-12.577, -36.131], [-17.146, -42.55], [-40.427, -53.064], [-49.161, -42.516], [-40.678, -31.175]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.952941179276, 0.635294139385, 0.313725501299, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 2, "cix": 2, "bm": 0, "ix": 10, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -0.516], [-10.313, -1.453], [-5.062, 0], [-1.547, 0.047], [-2.578, 2.484], [-0.14, 0.141], [0, 1.406], [0.094, 0.281], [0.609, 0.891], [1.781, 1.453], [0.14, 0.047], [5.109, 0.234], [0.563, -6.375]], "o": [[0, 2.906], [1.5, 0.234], [1.547, 0], [5.391, -0.234], [0.141, -0.094], [0.797, -0.891], [0, -0.281], [-0.094, -0.891], [-0.891, -1.547], [-0.094, -0.094], [-7.125, -5.719], [-5.812, 0], [-0.094, 0.469]], "v": [[-51.262, -42.5], [-40.949, -29.14], [-30.075, -28.625], [-25.387, -28.718], [-12.215, -32.281], [-11.793, -32.656], [-10.481, -36.125], [-10.574, -36.922], [-11.653, -39.64], [-15.824, -44.187], [-16.153, -44.422], [-40.34, -55.156], [-51.168, -43.953]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.72549021244, 0.380392163992, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 11", "np": 2, "cix": 2, "bm": 0, "ix": 11, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -1.666], [-1.735, 3.193], [-4.998, 0], [-0.833, -0.208], [-5.484, -6.941], [-0.139, -0.208], [-0.555, -2.221], [-0.139, 0.625], [2.985, 3.818], [0.208, 0.208], [4.512, 1.458], [0.833, 0], [2.777, -5.345], [0.902, -5.137]], "o": [[0.902, -5.137], [2.777, -5.345], [0.833, 0], [4.512, 1.458], [0.208, 0.208], [1.943, 2.499], [0.208, -0.625], [0.763, -2.846], [-0.139, -0.208], [-5.484, -6.941], [-0.833, -0.208], [-4.998, 0], [-1.735, 3.193], [-0.347, 1.735]], "v": [[-51.173, -46.888], [-46.592, -60.284], [-34.167, -69.794], [-31.668, -69.447], [-12.579, -54.176], [-12.024, -53.482], [-8.067, -46.332], [-7.512, -48.206], [-11.469, -58.688], [-12.024, -59.382], [-31.113, -74.653], [-33.612, -75], [-46.037, -65.49], [-50.618, -52.094]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.870588243008, 0.607843160629, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 12", "np": 2, "cix": 2, "bm": 0, "ix": 12, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.234, -2.531], [-3.375, -1.406], [-4.875, -1.359], [-1.172, 0.047], [-0.609, 0.141], [-1.031, 1.125], [-0.89, 3.187], [3, 3.797], [0.141, 0.14], [0.047, 0], [0.047, 0.047], [4.5, 1.453], [0.797, 0], [2.812, -5.344], [0.89, -5.109]], "o": [[0.328, 3.89], [1.406, 0.656], [1.406, 0.422], [0.656, -0.047], [1.5, -0.375], [2.063, -2.109], [0.797, -2.812], [-0.094, -0.187], [0, 0], [-0.047, -0.094], [-5.484, -6.937], [-0.844, -0.188], [-5.015, 0], [-1.734, 3.187], [-0.563, 2.812]], "v": [[-51.168, -43.953], [-45.918, -35.375], [-21.121, -37.672], [-17.278, -37.109], [-15.403, -37.39], [-11.653, -39.64], [-7.528, -48.219], [-11.465, -58.672], [-11.84, -59.187], [-11.887, -59.187], [-12.028, -59.375], [-31.106, -74.656], [-33.59, -74.984], [-46.059, -65.468], [-50.606, -52.109]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.831372559071, 0.458823531866, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 13", "np": 2, "cix": 2, "bm": 0, "ix": 13, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, -33.5], "ix": 2}, "a": {"a": 0, "k": [0, -33.5], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 13, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-2.719, -1.406], [-0.047, 0], [-2.438, -0.141], [-0.281, 0], [-1.594, 0], [0, 0], [-1.641, 0.281], [-2.438, 5.25], [0.469, 0.281], [3.079, 1.717], [0, 0]], "o": [[0, 0], [3, 1.36], [0, 0], [1.828, 0.375], [0.281, 0], [1.594, 0.047], [0, 0], [4.828, -0.047], [4.219, -0.609], [-0.422, -0.375], [-2.862, -1.699], [0, 0], [0, 0]], "v": [[2.066, -33.699], [9.394, -30.359], [17.879, -26.234], [17.972, -26.234], [24.394, -25.484], [25.238, -25.437], [30.019, -25.344], [30.769, -25.344], [41.363, -25.906], [52.754, -33.687], [51.441, -34.625], [42.485, -39.753], [7.933, -39.753]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.866666674614, 0.521568655968, 0.211764708161, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 50, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.079, -0.079], [0.047, -0.047], [-0.047, 0.047], [-0.069, 0.098]], "o": [[-0.047, 0.047], [0.047, 0], [0.083, -0.093], [-0.07, 0.087]], "v": [[-13.434, -34.062], [-13.528, -33.921], [-13.387, -34.015], [-13.232, -34.325]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.952941179276, 0.635294139385, 0.313725501299, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.074, 0.407], [0.244, -0.345]], "o": [[-0.073, 0.402], [0.262, -0.326]], "v": [[-12.684, -35.421], [-13.232, -34.325]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.952941179276, 0.635294139385, 0.313725501299, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [2.719, -1.406], [0.047, 0], [2.438, -0.141], [0.281, 0], [1.594, 0], [0, 0], [1.641, 0.281], [2.438, 5.25], [-0.469, 0.281], [-3.079, 1.717], [0, 0]], "o": [[0, 0], [-3, 1.36], [0, 0], [-1.828, 0.375], [-0.281, 0], [-1.594, 0.047], [0, 0], [-4.828, -0.047], [-4.219, -0.609], [0.422, -0.375], [2.862, -1.699], [0, 0], [0, 0]], "v": [[-2.121, -33.699], [-9.45, -30.359], [-17.934, -26.234], [-18.028, -26.234], [-24.45, -25.484], [-25.293, -25.437], [-30.075, -25.344], [-30.824, -25.344], [-41.418, -25.906], [-52.809, -33.687], [-51.496, -34.625], [-42.54, -39.753], [-7.988, -39.753]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.866666674614, 0.521568655968, 0.211764708161, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 50, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.869, -0.343], [0, 0], [-1.922, -0.562], [-0.187, 3.843], [0, 0], [5.156, 3.094], [1.266, 0.703], [3.281, 1.781], [1.781, 0.984], [3.609, 1.828], [0, 0], [-2.063, -0.938], [-6.188, -3.703], [0.094, -3.984]], "o": [[-0.03, 0.934], [0, 0], [1.547, 1.969], [3.703, 1.078], [0, 0], [0.281, -6.047], [-1.265, -0.75], [-3.047, -1.781], [-1.734, -0.938], [-3.422, -1.781], [0, 0], [2.391, 1.031], [7.172, 3.234], [3.422, 2.016], [0, 0]], "v": [[28.389, 63.583], [26.908, 65.686], [20.972, 68.031], [26.316, 71.922], [33.816, 65.969], [37.05, -11.703], [29.128, -26.515], [25.332, -28.718], [15.863, -34.062], [10.566, -36.922], [-0.028, -42.359], [-0.028, -34.625], [6.582, -31.672], [25.519, -22.015], [30.863, -12.219]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.952941179276, 0.635294139385, 0.313725501299, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 2, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.094, -3.281], [0, 0], [0, 0], [0, 0], [2.016, 3.141], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.188, -4.265], [0, 0], [0, 0], [2.016, 2.109]], "v": [[28.941, -8.89], [28.389, 63.583], [31.941, 57.719], [34.707, -8.375], [32.597, -19.859], [27.113, -17.515], [27.113, -17.515]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.57647061348, 0.027450980619, 0.227450981736, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 50, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 2, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-8.737, -5.232], [0.253, -6.032], [0, 0], [3.087, -2.266], [5.74, 4.348], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[10.506, 5.332], [5.18, 3.101], [0, 0], [-0.283, 3.819], [-7.078, 5.195], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[22.328, -50.463], [51.452, -34.603], [59.402, -19.785], [53.964, 53.604], [48.698, 63.208], [24.09, 70.913], [30.402, 66.876], [31.907, -21.168], [-0.016, -39.529], [-0.016, -50.463]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.831372559071, 0.458823531866, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 2, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.234, -6.047], [0, 0], [-3.703, 1.078], [-1.594, 1.969], [0, 0], [0.029, 0.938], [0, 0], [-3.422, 2.016], [-7.172, 3.234], [-2.344, 1.031], [0, 0], [3.422, -1.828], [1.734, -0.938], [3.047, -1.781], [1.219, -0.75]], "o": [[0, 0], [0.141, 3.843], [1.969, -0.562], [0, 0], [-0.873, -0.343], [0, 0], [-0.14, -3.984], [6.188, -3.703], [2.016, -0.938], [0, 0], [-3.609, 1.828], [-1.828, 0.938], [-3.281, 1.781], [-1.265, 0.75], [-5.203, 3.094]], "v": [[-37.106, -11.703], [-33.825, 65.969], [-26.371, 71.922], [-20.981, 68.031], [-27.078, 65.64], [-28.565, 63.53], [-30.871, -12.219], [-25.528, -22.015], [-6.59, -31.672], [-0.028, -34.625], [-0.028, -42.359], [-10.574, -36.922], [-15.918, -34.062], [-25.387, -28.718], [-29.137, -26.515]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.952941179276, 0.635294139385, 0.313725501299, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 9", "np": 2, "cix": 2, "bm": 0, "ix": 9, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.094, -3.281], [0, 0], [0, 0], [0, 0], [-2.016, 3.141], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.188, -4.265], [0, 0], [0, 0], [-2.016, 2.109]], "v": [[-28.941, -8.89], [-28.565, 63.53], [-31.79, 58.039], [-34.707, -8.375], [-32.597, -19.859], [-27.113, -17.515], [-27.113, -17.515]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.57647061348, 0.027450980619, 0.227450981736, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 50, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 2, "cix": 2, "bm": 0, "ix": 10, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [8.737, -5.232], [-0.253, -6.032], [0, 0], [-3.087, -2.266], [-5.74, 4.348], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-10.506, 5.332], [-5.18, 3.101], [0, 0], [0.283, 3.819], [7.078, 5.195], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-22.359, -50.463], [-51.483, -34.603], [-59.434, -19.785], [-53.996, 53.604], [-48.729, 63.208], [-24.121, 70.913], [-30.434, 66.876], [-31.939, -21.168], [-0.016, -39.529], [-0.016, -50.463]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.831372559071, 0.458823531866, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 11", "np": 2, "cix": 2, "bm": 0, "ix": 11, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.265, 0.75], [-3.281, 1.781], [-1.828, 0.938], [-3.609, 1.828], [-3.422, -1.781], [-1.734, -0.938], [-3.047, -1.781], [-1.265, -0.75], [-0.04, -0.025], [0, 0], [6.02, 2.715], [2.391, 1.031], [2.016, -0.937], [5.08, -2.878], [0, 0], [-0.043, 0.026]], "o": [[3.047, -1.781], [1.734, -0.938], [3.422, -1.828], [3.609, 1.828], [1.781, 0.984], [3.281, 1.781], [1.266, 0.703], [0.041, 0.025], [0, 0], [-5.077, -2.876], [-2.063, -0.937], [-2.344, 1.031], [-6.023, 2.717], [0, 0], [0.042, -0.027], [1.219, -0.75]], "v": [[-25.387, -25.39], [-15.918, -30.734], [-10.574, -33.594], [-0.028, -39.031], [10.566, -33.594], [15.863, -30.734], [25.332, -25.39], [29.128, -23.187], [29.243, -23.106], [22.59, -20.368], [6.582, -28.344], [-0.028, -31.296], [-6.59, -28.344], [-22.606, -20.363], [-29.258, -23.101], [-29.137, -23.187]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.800000011921, 0.06274510175, 0.290196090937, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.991, -0.593], [0.469, -0.422], [0, 0], [-1.402, 0.875], [0, 0]], "o": [[-0.562, 0.328], [0, 0], [1.047, -1.183], [0, 0], [-0.969, 0.549]], "v": [[-25.528, -18.687], [-27.121, -17.515], [-32.981, -20], [-29.258, -23.101], [-22.606, -20.363]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.874509811401, 0.196078434587, 0.376470595598, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.048, -1.23], [0, 0], [0.562, 0.328], [0.971, 0.55], [0, 0]], "o": [[0, 0], [-0.469, -0.422], [-0.994, -0.595], [0, 0], [1.402, 0.873]], "v": [[32.925, -20], [27.113, -17.515], [25.519, -18.687], [22.59, -20.368], [29.243, -23.106]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.874509811401, 0.196078434587, 0.376470595598, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 12", "np": 3, "cix": 2, "bm": 0, "ix": 12, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.721, 0], [-0.664, 0.284], [0, 0], [1.142, 0.47], [0, 0], [0.723, 0], [0.664, -0.284], [0, 0], [-1.142, -0.47], [0, 0]], "o": [[0.723, 0], [0, 0], [1.142, -0.47], [0, 0], [-0.668, -0.288], [-0.721, 0], [0, 0], [-1.142, 0.47], [0, 0], [0.668, 0.288]], "v": [[-0.017, -11.497], [2.075, -11.923], [63.715, -37.296], [63.715, -39.852], [2.08, -65.22], [-0.017, -65.651], [-2.107, -65.225], [-63.747, -39.853], [-63.747, -37.296], [-2.111, -11.928]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.913725495338, 0.207843139768, 0.396078437567, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 13", "np": 2, "cix": 2, "bm": 0, "ix": 13, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.281, -1.219], [-0.75, -0.188], [0, 0], [0, 0], [0, -2.25], [0, 0], [-2.484, 1.078], [0, 0], [-2.063, 0.891], [0, 0], [0, 0], [-0.984, -2.812], [0.375, 0.984], [0, 0], [0.094, 0.187], [1.125, 0.469], [0, 0], [2.531, -1.078], [0, 0], [0.656, -1.734], [0.094, -0.188]], "o": [[0.891, -2.484], [0.094, 0.047], [0, 0], [2.063, 0.891], [0, 0], [2.531, 1.078], [0, 0], [0, -2.25], [0, 0], [0, 0], [0, 0], [-0.281, -1.219], [0, 0], [-0.094, -0.188], [-0.703, -1.734], [0, 0], [-2.484, -1.078], [0, 0], [-1.125, 0.469], [-0.094, 0.187], [-0.375, 0.984]], "v": [[-74.184, -33.875], [-69.309, -35.468], [-69.168, -35.421], [-7.34, -9.078], [-3.965, -3.922], [-3.965, 18.859], [3.91, 18.859], [3.91, -3.922], [7.285, -9.078], [69.16, -35.468], [69.16, -35.421], [74.175, -33.875], [73.238, -37.156], [73.238, -37.203], [73.004, -37.765], [70.238, -41.187], [3.91, -69.547], [-3.965, -69.547], [-70.293, -41.187], [-73.012, -37.765], [-73.246, -37.156]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.980392158031, 0.372549027205, 0.498039215803, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 14", "np": 2, "cix": 2, "bm": 0, "ix": 14, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4.406, -1.875], [0, 0], [2.484, 1.078], [0, 0], [-3.094, 8.156], [-1.266, 0.516], [0, 0], [-0.656, -1.734], [-0.094, -0.188], [-0.281, -1.219], [0, 0]], "o": [[0, 0], [-2.531, 1.078], [0, 0], [-5.063, -2.156], [0.75, -2.016], [0, 0], [1.125, 0.469], [0.094, 0.187], [0.375, 0.984], [0, 0], [1.828, 8.813]], "v": [[70.293, -9.453], [3.965, 18.859], [-3.91, 18.859], [-70.238, -9.453], [-73.238, -37.203], [-70.238, -41.187], [70.293, -41.187], [73.012, -37.765], [73.246, -37.156], [74.184, -33.875], [74.184, -33.828]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.913725495338, 0.207843139768, 0.396078437567, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 15", "np": 2, "cix": 2, "bm": 0, "ix": 15, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.516, 1.078], [0, 0], [-2.516, 1.078], [0, 0]], "o": [[0, 0], [2.516, 1.078], [0, 0], [-2.516, 1.078]], "v": [[-3.969, 17.956], [-3.969, 27.727], [3.937, 27.727], [3.937, 17.956]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.57647061348, 0.027450980619, 0.227450981736, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 50, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 16", "np": 2, "cix": 2, "bm": 0, "ix": 16, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [-2.516, 1.078], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [2.516, 1.078], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-66.98, -11.865], [-66.201, 1.133], [-3.969, 27.727], [3.937, 27.727], [66.17, 1.133], [66.949, -11.865], [-0.016, 9.219]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.800000011921, 0.06274510175, 0.290196090937, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 17", "np": 2, "cix": 2, "bm": 0, "ix": 17, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.516, 1.078], [0, 0], [-2.576, 0.659], [0, 0]], "o": [[0, 0], [2.576, 0.659], [0, 0], [-2.516, 1.078]], "v": [[-3.969, 17.956], [-3.969, 74.506], [3.937, 74.506], [3.937, 17.956]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.980392158031, 0.372549027205, 0.498039215803, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 18", "np": 2, "cix": 2, "bm": 0, "ix": 18, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [-6.289, -2.516], [0, 0], [-0.838, -0.18], [-2.575, 0.659], [-0.779, 0.299], [0, 0], [-0.419, 6.708], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0.419, 6.708], [0, 0], [0.779, 0.299], [2.576, 0.659], [0.839, -0.18], [0, 0], [6.289, -2.516], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-66.98, -11.865], [-66.201, 1.133], [-63.865, 40.365], [-52.904, 55.459], [-6.365, 73.787], [-3.969, 74.506], [3.937, 74.506], [6.333, 73.787], [52.873, 55.459], [63.834, 40.365], [66.17, 1.133], [66.949, -11.865], [-0.016, 9.219]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.956862747669, 0.317647069693, 0.439215689898, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 19", "np": 2, "cix": 2, "bm": 0, "ix": 19, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 74.912], "ix": 2}, "a": {"a": 0, "k": [0, 74.912], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 20, "s": [100, 100]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 22.5, "s": [100, 95]}, {"t": 25, "s": [100, 100]}], "ix": 3, "x": "var $bm_rt;\nvar fx = effect('Kleaner');\nvar doAnticipation = fx(1).value;\nvar doInterpolation = fx(2).value;\nvar doFollowThrough = fx(3).value;\nvar damping = $bm_div(fx(15).value, 10);\nvar elasticity = $bm_div(fx(13).value, 10);\nvar bounce = fx(17).value;\nvar anticipationDuration = fx(5).value;\nvar anticipationQuantity = $bm_div(fx(6).value, 100);\nvar slowIn = $bm_div(fx(9).value, 100);\nvar slowOut = $bm_div(fx(10).value, 100);\nvar spatialMode = fx(21).value;\nvar spatialDoInterpolation = fx(20).value;\nvar moBlurPrecision = fx(32).value;\nvar elasticityRandom = fx(14).value;\nvar dampingRandom = fx(16).value;\nvar softBody = fx(28).value;\nvar flexibility = $bm_div(fx(29).value, 100);\nvar doOverlap = fx(23).value;\nvar overlapDuration = fx(24).value;\nvar overlapRandom = fx(25).value;\nvar threshold = $bm_div($bm_div(1, moBlurPrecision), 1000);\nvar zeroValue;\nif ($bm_isInstanceOfArray(value)) {\n    if (value.length == 2)\n        zeroValue = [\n            0,\n            0\n        ];\n    else if (value.length == 3)\n        zeroValue = [\n            0,\n            0,\n            0\n        ];\n    else if (value.length == 4)\n        zeroValue = [\n            0,\n            0,\n            0,\n            0\n        ];\n} else\n    zeroValue = 0;\nvar isThisSpatial = isSpatial(thisProperty);\nvar isThisPosition = thisProperty === $bm_transform.position;\nvar simulate = false;\nif (isThisSpatial) {\n    doInterpolation = doInterpolation && spatialDoInterpolation;\n    if (isThisPosition && thisProperty.numKeys > 0) {\n        doOverlap = false;\n        simulate = false;\n    } else {\n        simulate = spatialMode == 2;\n        doOverlap = overlapDuration != 0 && doOverlap && simulate;\n    }\n} else {\n    doOverlap = false;\n}\nif (!doOverlap) {\n    overlapDuration = 0;\n}\nif (simulate && softBody && isThisSpatial && !isThisPosition && (doFollowThrough || doOverlap)) {\n    var distanceRatio = $bm_div(length(valueAtTime(0), $bm_transform.anchorPoint), $bm_div(thisLayer.width, 2));\n    distanceRatio = $bm_div($bm_sum(1, $bm_mul(distanceRatio, flexibility)), 2);\n    if (doFollowThrough) {\n        elasticity = $bm_div(elasticity, distanceRatio);\n        damping = $bm_div(damping, distanceRatio);\n    }\n    if (doOverlap) {\n        overlapDuration = $bm_mul(overlapDuration, distanceRatio);\n    }\n}\nseedRandom(0, true);\nif (doFollowThrough) {\n    if (elasticityRandom > 0)\n        elasticity = addNoise(elasticity, elasticityRandom);\n    if (dampingRandom > 0)\n        damping = addNoise(damping, dampingRandom);\n}\nif (doOverlap) {\n    if (doOverlap && overlapRandom > 0)\n        overlapDuration = addNoise(overlapDuration, overlapRandom);\n}\nvar result = value;\nfunction isSpatial(prop) {\n    if (!(prop.value instanceof Array))\n        return false;\n    if (prop.value.length != 2 && prop.value.length != 3)\n        return false;\n    try {\n        if (typeof prop.speed !== 'undefined')\n            return true;\n    } catch (e) {\n        return false;\n    }\n}\nfunction addNoise(val, quantity) {\n    var randomValue = random(0.9, 1.1);\n    var noiseValue = noise($bm_mul(valueAtTime(0), randomValue));\n    noiseValue = $bm_mul(noiseValue, $bm_div(quantity, 100));\n    return $bm_mul(val, $bm_sum(noiseValue, 1));\n}\nfunction isAfterLastKey() {\n    if (numKeys == 0)\n        return false;\n    var nKey = nearestKey(time);\n    return nKey.time <= time && nKey.index == numKeys;\n}\nfunction isStill(t, threshold) {\n    var d = $bm_sub(valueAtTime(t), valueAtTime($bm_sum(t, framesToTime(1))));\n    if ($bm_isInstanceOfArray(d)) {\n        for (var i = 0; i < d.length; i++) {\n            d[i] = Math.abs(d[i]);\n            if (d[i] >= threshold) {\n                return false;\n            }\n        }\n        return true;\n    } else {\n        d = Math.abs(d);\n        return d < threshold;\n    }\n}\nfunction bezierInterpolation(t, tMin, tMax, value1, value2, bezierPoints) {\n    if (arguments.length !== 5 && arguments.length !== 6)\n        return t;\n    var a = $bm_sub(value2, value1);\n    var b = $bm_sub(tMax, tMin);\n    if (b == 0)\n        return t;\n    var c = clamp($bm_div($bm_sub(t, tMin), b), 0, 1);\n    if (!(bezierPoints instanceof Array) || bezierPoints.length !== 4)\n        bezierPoints = [\n            0.33,\n            0,\n            0.66,\n            1\n        ];\n    return $bm_sum($bm_mul(a, h(c, bezierPoints)), value1);\n    function h(f, g) {\n        var x = $bm_mul(3, g[0]);\n        var j = $bm_sub($bm_mul(3, $bm_sub(g[2], g[0])), x);\n        var k = $bm_sub($bm_sub(1, x), j);\n        var l = $bm_mul(3, g[1]);\n        var m = $bm_sub($bm_mul(3, $bm_sub(g[3], g[1])), l);\n        var n = $bm_sub($bm_sub(1, l), m);\n        var d = f;\n        for (var i = 0; i < 5; i++) {\n            var z = $bm_sub($bm_mul(d, $bm_sum(x, $bm_mul(d, $bm_sum(j, $bm_mul(d, k))))), f);\n            if (Math.abs(z) < 0.001)\n                break;\n            d = $bm_sub(d, $bm_div(z, $bm_sum(x, $bm_mul(d, $bm_sum($bm_mul(2, j), $bm_mul($bm_mul(3, k), d))))));\n        }\n        return $bm_mul(d, $bm_sum(l, $bm_mul(d, $bm_sum(m, $bm_mul(d, n)))));\n    }\n}\nfunction getPropWorldSpeed(t, prop) {\n    return length(getPropWorldVelocity(t, prop));\n}\nfunction getPrevKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time <= t)\n        return nKey;\n    if (nKey.index > 1)\n        return key($bm_sub(nKey.index, 1));\n    return null;\n}\nfunction getNextKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time >= t)\n        return nKey;\n    if (nKey.index < numKeys)\n        return key($bm_sum(nKey.index, 1));\n    return null;\n}\nfunction getPropWorldVelocity(t, prop) {\n    return $bm_mul($bm_sub(getPropWorldValue($bm_sum(t, 0.005), prop), getPropWorldValue($bm_sub(t, 0.005), prop)), 100);\n}\nfunction getLayerWorldPos(t, l) {\n    return l.toWorld(l.anchorPoint, t);\n}\nfunction getPropWorldValue(t, prop) {\n    if (isPosition(prop))\n        return getLayerWorldPos(t, thisLayer);\n    return thisLayer.toWorld(prop.valueAtTime(t), t);\n}\nfunction isPosition(prop) {\n    return prop === $bm_transform.position;\n}\nfunction isKeyTop(k, axis) {\n    var prevSpeed = velocityAtTime($bm_sub(k.time, threshold));\n    var nextSpeed = velocityAtTime($bm_sum(k.time, threshold));\n    if ($bm_isInstanceOfArray(value)) {\n        prevSpeed = prevSpeed[axis];\n        nextSpeed = nextSpeed[axis];\n    }\n    if (Math.abs(prevSpeed) < 0.01 || Math.abs(nextSpeed) < 0.01)\n        return true;\n    return $bm_mul(prevSpeed, nextSpeed) < 0;\n}\nfunction anticipate() {\n    var anticipation = zeroValue;\n    if (isAfterLastKey())\n        return anticipation;\n    if (numKeys < 2)\n        return anticipation;\n    var nextKey = getNextKey(time);\n    var aKey = nextKey;\n    if (!isStill(aKey.time - 0.1, 0.1)) {\n        aKey = getPrevKey(time);\n        if (!isStill(aKey.time - 0.1, 0.1))\n            return anticipation;\n    }\n    if (aKey.index == numKeys)\n        return anticipation;\n    var anticipationMiddle = aKey.time;\n    var anticipationStart = $bm_sub(anticipationMiddle, anticipationDuration);\n    var anticipationEnd = key(aKey.index + 1).time;\n    var startValue = anticipation;\n    var midValue = $bm_mul($bm_sum($bm_neg(valueAtTime($bm_sum(anticipationMiddle, anticipationDuration))), aKey.value), anticipationQuantity);\n    var endValue = anticipation;\n    if (time < anticipationStart) {\n        return anticipation;\n    } else if (time < anticipationMiddle) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue[i], midValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue, midValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else if (time <= anticipationEnd) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue[i], endValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue, endValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else {\n        return anticipation;\n    }\n}\nfunction followThroughAtTime(t) {\n    var fThrough = zeroValue;\n    if (elasticity == 0)\n        return fThrough;\n    var propSpeed;\n    if (!simulate) {\n        if (numKeys < 2)\n            return fThrough;\n        if (nearestKey(t).index == 1)\n            return fThrough;\n        propSpeed = length(velocityAtTime(t));\n        if (propSpeed >= threshold)\n            return fThrough;\n    } else {\n        propSpeed = getPropWorldSpeed(t, thisProperty);\n        if (propSpeed >= threshold)\n            return fThrough;\n    }\n    var fThroughStart = 0;\n    var fThroughTime = 0;\n    if (simulate) {\n        var speedI = getPropWorldSpeed(t, thisProperty);\n        var i = t;\n        while (speedI < threshold && i > 0) {\n            i = $bm_sub(i, $bm_div(thisComp.frameDuration, moBlurPrecision));\n            speedI = getPropWorldSpeed(i, thisProperty);\n        }\n        fThroughStart = i;\n    } else {\n        var fThroughKey = getPrevKey(t);\n        fThroughStart = fThroughKey.time;\n    }\n    if (fThroughStart == 0)\n        return fThrough;\n    fThroughTime = $bm_sub(t, fThroughStart);\n    if (simulate)\n        fThrough = $bm_div(getPropWorldVelocity($bm_sub(fThroughStart, thisComp.frameDuration), thisProperty), 2);\n    else\n        fThrough = $bm_div(velocityAtTime($bm_sub(fThroughStart, thisComp.frameDuration)), 2);\n    if (bounce) {\n        var cycleDamp = Math.exp($bm_mul($bm_mul(fThroughTime, damping), 0.1));\n        var damp = $bm_div(Math.exp($bm_mul(fThroughTime, damping)), $bm_div(elasticity, 2));\n        var cycleDuration = $bm_div(1, $bm_mul(elasticity, 2));\n        cycleDuration = Math.round(timeToFrames(cycleDuration));\n        cycleDuration = framesToTime(cycleDuration);\n        var midDuration = $bm_div(cycleDuration, 2);\n        var maxValue = $bm_mul(fThrough, midDuration);\n        var cycvarime = fThroughTime;\n        var numEndCycles = 1;\n        while (cycvarime > cycleDuration) {\n            cycvarime = $bm_sub(cycvarime, cycleDuration);\n            cycleDuration = $bm_div(cycleDuration, cycleDamp);\n            cycleDuration = Math.round(timeToFrames(cycleDuration));\n            if (cycleDuration < 2) {\n                cycleDuration = 2;\n                numEndCycles++;\n            }\n            cycleDuration = framesToTime(cycleDuration);\n            midDuration = $bm_div(cycleDuration, 2);\n            maxValue = $bm_div($bm_mul(fThrough, midDuration), damp);\n            if (numEndCycles > 100 / damping && maxValue < threshold)\n                return zeroValue;\n        }\n        if (cycvarime < midDuration)\n            fThrough = bezierInterpolation(cycvarime, 0, midDuration, 0, maxValue, [\n                0,\n                0.1,\n                slowIn,\n                1\n            ]);\n        else\n            fThrough = bezierInterpolation(cycvarime, midDuration, cycleDuration, maxValue, 0, [\n                $bm_sub(1, slowIn),\n                0,\n                1,\n                0.9\n            ]);\n    } else {\n        var damp = Math.exp($bm_mul(fThroughTime, damping));\n        var sinus = $bm_mul($bm_mul($bm_mul(elasticity, fThroughTime), 2), Math.PI);\n        sinus = Math.sin(sinus);\n        sinus = $bm_mul($bm_div(0.3, elasticity), sinus);\n        sinus = $bm_div(sinus, damp);\n        if (Math.abs(sinus) < $bm_div(threshold, 100))\n            return 0;\n        fThrough = $bm_mul(fThrough, sinus);\n        if (threshold > 0) {\n            fThrough = $bm_mul(fThrough, $bm_sub(1, $bm_div(propSpeed, threshold)));\n        }\n    }\n    if (bounce) {\n        var prevValue = valueAtTime($bm_sub(fThroughStart, thisComp.frameDuration));\n        var startValue = valueAtTime(fThroughStart);\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < prevValue.length; i++) {\n                if (prevValue[i] > startValue[i])\n                    fThrough[i] = Math.abs(fThrough[i]);\n                if (prevValue[i] < startValue[i])\n                    fThrough[i] = $bm_neg(Math.abs(fThrough[i]));\n            }\n        } else {\n            if (prevValue > startValue)\n                fThrough = Math.abs(fThrough);\n            if (prevValue < startValue)\n                fThrough = $bm_neg(Math.abs(fThrough));\n        }\n    }\n    if (simulate) {\n        if (!isThisPosition) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer));\n            fThrough = $bm_sub(thisLayer.fromWorld(fThrough), thisLayer.anchorPoint);\n        } else if (thisLayer.hasParent) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer.parent));\n            fThrough = $bm_sub(thisLayer.parent.fromWorld(fThrough), thisLayer.parent.anchorPoint);\n        }\n    }\n    return fThrough;\n}\nfunction followThrough() {\n    var propSpeed = length(velocity);\n    if (propSpeed < threshold)\n        return followThroughAtTime($bm_sub(time, overlapDuration));\n    var fThrough = zeroValue;\n    var t = time;\n    while (t > 0) {\n        t = $bm_sub(t, thisComp.frameDuration);\n        if (simulate)\n            propSpeed = getPropWorldSpeed($bm_sub(t, overlapDuration), thisProperty);\n        else\n            propSpeed = length(velocityAtTime(t));\n        if (propSpeed < threshold) {\n            fThrough = followThroughAtTime($bm_sub(t, overlapDuration));\n            break;\n        }\n    }\n    return linear(time, t, $bm_sum(t, $bm_mul(anticipationDuration, 2)), fThrough, zeroValue);\n}\nfunction smartSmooth(axis) {\n    var startKey = nearestKey(time);\n    var endKey = startKey;\n    if (time == startKey.time)\n        return 0;\n    if (time < startKey.time && startKey.index == 1)\n        return 0;\n    if (time > startKey.time && startKey.index == numKeys)\n        return 0;\n    if (time < startKey.time)\n        startKey = key($bm_sub(startKey.index, 1));\n    if (time > startKey.time)\n        endKey = key($bm_sum(startKey.index, 1));\n    var sI = 0.66;\n    var sO = 0.33;\n    var sIV = 1;\n    var sOV = 0;\n    var sVal = startKey.value;\n    var eVal = endKey.value;\n    if ($bm_isInstanceOfArray(value)) {\n        sVal = sVal[axis];\n        eVal = eVal[axis];\n    }\n    var sTime = startKey.time;\n    var eTime = endKey.time;\n    if (isKeyTop(startKey, axis))\n        sO = slowOut;\n    else {\n        var prevKey = key($bm_sub(startKey.index, 1));\n        var pVal = prevKey.value;\n        if ($bm_isInstanceOfArray(value))\n            pVal = pVal[axis];\n        sOV = $bm_div($bm_sub(sVal, pVal), $bm_sub(eVal, pVal));\n    }\n    if (isKeyTop(endKey, axis)) {\n        sI = slowIn;\n        if (endKey.index != numKeys) {\n            var nextKey = key($bm_sum(endKey.index, 1));\n            var nVal = nextKey.value;\n            if ($bm_isInstanceOfArray(value))\n                nVal = nVal[axis];\n            if (Math.abs(nVal - eVal) < 0.01 && doFollowThrough)\n                sI = 1;\n        }\n    } else {\n        var nextKey = key($bm_sum(endKey.index, 1));\n        var nVal = nextKey.value;\n        if ($bm_isInstanceOfArray(value))\n            nVal = nVal[axis];\n        sIV = $bm_div($bm_sub(eVal, sVal), $bm_sub(nVal, sVal));\n    }\n    if (endKey.index == numKeys && doFollowThrough) {\n        sI = 1;\n    }\n    var val = value;\n    if ($bm_isInstanceOfArray(value))\n        val = val[axis];\n    return $bm_sub(bezierInterpolation(time, sTime, eTime, sVal, eVal, [\n        sO,\n        sOV,\n        sI,\n        sIV\n    ]), val);\n}\nfunction overlap() {\n    var ol = zeroValue;\n    if (isThisPosition && !hasParent)\n        return zeroValue;\n    ol = $bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisProperty), getPropWorldValue(time, thisProperty));\n    var motionRatio = $bm_div($bm_div(length(zeroValue, ol), thisLayer.width), 2);\n    if (isThisPosition) {\n        var originalDistance = length(valueAtTime(0));\n        motionRatio = $bm_div(length(zeroValue, ol), thisComp.width);\n    }\n    ol = $bm_sum(ol, getPropWorldValue(time, thisProperty));\n    ol = $bm_sum(ol, $bm_mul($bm_mul($bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisLayer.anchorPoint), ol), motionRatio), flexibility));\n    ol = thisLayer.fromWorld(ol);\n    if (!isThisPosition)\n        ol = $bm_sub(ol, value);\n    else {\n        ol = linear(flexibility, 0, 100, $bm_div(ol, 2), 0);\n        var prevParentWorldPos = getLayerWorldPos($bm_sub(time, overlapDuration), parent);\n        ol = $bm_sum(ol, $bm_mul($bm_mul($bm_mul(thisLayer.fromWorld(prevParentWorldPos), motionRatio), flexibility), 5));\n    }\n    return ol;\n}\nvar okToGo = false;\nif (simulate && fx.enabled)\n    okToGo = true;\nelse if (numKeys > 1 && fx.enabled)\n    okToGo = true;\nif (okToGo) {\n    var smartSmoothResult = zeroValue;\n    if (doInterpolation) {\n        if ($bm_isInstanceOfArray(value)) {\n            if (value.length == 2)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1)\n                ];\n            else if (value.length == 3)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2)\n                ];\n            else if (value.length == 4)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2),\n                    smartSmooth(3)\n                ];\n        } else {\n            smartSmoothResult = smartSmooth(0);\n        }\n    }\n    if (doAnticipation)\n        result = $bm_sum(result, anticipate());\n    result = $bm_sum(result, smartSmoothResult);\n    if (doFollowThrough)\n        result = $bm_sum(result, followThrough());\n    if (doOverlap)\n        result = $bm_sum(result, overlap());\n}\n$bm_rt = result;"}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 35, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [4]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 45, "s": [-4]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [4]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 55, "s": [-4]}, {"t": 60, "s": [0]}], "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 19, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 125, "st": 0, "bm": 0}], "markers": []}