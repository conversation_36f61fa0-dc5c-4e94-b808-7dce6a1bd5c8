import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:shortie/pages/feed_page/view/feed_view.dart';
import 'package:shortie/pages/message_page/view/message_view.dart';
import 'package:shortie/pages/profile_page/view/profile_view.dart';
import 'package:shortie/pages/reels_page/view/reels_view.dart';
import 'package:shortie/pages/stream_page/view/stream_view.dart';
import 'package:shortie/routes/app_routes.dart';
import 'package:shortie/ui/send_gift_on_video_bottom_sheet_ui.dart';
import 'package:shortie/utils/branch_io_services.dart';
import 'package:shortie/utils/socket_services.dart';
import 'package:shortie/utils/utils.dart';

class BottomBarController extends GetxController {
  int selectedTabIndex = 0;
  PageController pageController = PageController();

  @override
  void onInit() {
    init();
    super.onInit();
  }

  void init() async {
    selectedTabIndex = 0;

    await SocketServices.socketConnect();

    SendGiftOnVideoBottomSheetUi.onGetGift();

    if (BranchIoServices.eventType == "Post") {
      await 500.milliseconds.delay();
      onChangeBottomBar(2);
    } else if (BranchIoServices.eventType == "Profile") {
      await 500.milliseconds.delay();
      onChangeBottomBar(2);
      if (BranchIoServices.eventId != "") {
        Get.toNamed(AppRoutes.previewUserProfilePage, arguments: BranchIoServices.eventId);
      }
    } else if (BranchIoServices.eventType == "Video") {
      // Handle video link clicks - Use safer approach
      await 500.milliseconds.delay();
      if (BranchIoServices.eventId != "") {
        try {
          Utils.showLog("Navigating to video with ID: ${BranchIoServices.eventId}");

          // Navigate to reels page and let it handle the specific video
          onChangeBottomBar(0);

          // Store the video ID for the reels controller to use
          await 300.milliseconds.delay();

          // Try to find and navigate to the specific video in reels
          _navigateToSpecificVideo(BranchIoServices.eventId);

        } catch (e) {
          Utils.showLog("Error navigating to video: $e");
          // Fallback: just navigate to reels page
          onChangeBottomBar(0);
          Get.snackbar(
            "Info",
            "Video opened in reels feed.",
            snackPosition: SnackPosition.BOTTOM,
          );
        }
      } else {
        Utils.showLog("Video link clicked but no video ID provided");
        // Fallback: navigate to reels page
        onChangeBottomBar(0);
      }
    }

    // Reset Branch.io event data after processing
    BranchIoServices.eventId = "";
    BranchIoServices.eventType = "";
  }

  List bottomBarPages = [
    const ReelsView(),
    const StreamView(),
    const FeedView(),
    const MessageView(),
    const ProfileView(),
  ];

  void onChangeBottomBar(int index) {
    if (index != selectedTabIndex) {
      selectedTabIndex = index;
      update(["onChangeBottomBar"]);
    }
  }

  void _navigateToSpecificVideo(String videoId) {
    try {
      Utils.showLog("Attempting to navigate to specific video: $videoId");

      // This is a simplified approach - just show a message and let user browse
      // In a production app, you might want to implement video search/filtering
      Get.snackbar(
        "Video Link Opened",
        "Browse the reels to find your video.",
        snackPosition: SnackPosition.BOTTOM,
        duration: Duration(seconds: 3),
      );

    } catch (e) {
      Utils.showLog("Error in _navigateToSpecificVideo: $e");
    }
  }
}
